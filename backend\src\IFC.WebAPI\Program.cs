using Microsoft.EntityFrameworkCore;
using IFC.Infrastructure.Data;
using IFC.Application.Interfaces;
using IFC.Application.Services;
using IFC.Infrastructure.Repositories;
using IFC.Domain.Interfaces;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// Configure Entity Framework with SQL Server (same as VB.NET connections)
// SCM Database (Production, Recipes, Inventory) - replicates VB.NET SqlConn
builder.Services.AddDbContext<IFCDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("SCMConnection")));

// POS Database (Point of Sale transactions) - replicates VB.NET SqlConnPOS
builder.Services.AddDbContext<POSDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("POSConnection")));

// Register repositories (Data Access Layer)
builder.Services.AddScoped<IProductRepository, ProductRepository>();
builder.Services.AddScoped<IRecipeRepository, RecipeRepository>();
builder.Services.AddScoped<IInventoryRepository, InventoryRepository>();
builder.Services.AddScoped<IProductionOrderRepository, ProductionOrderRepository>();
builder.Services.AddScoped<IPOSRepository, POSRepository>(); // POS database operations

// Register services (Business Logic Layer)
builder.Services.AddScoped<IProductionService, ProductionService>();
builder.Services.AddScoped<IRecursiveProductionProcessor, RecursiveProductionProcessor>();

// Add AutoMapper for DTO mapping
builder.Services.AddAutoMapper(typeof(Program));

// Configure CORS to allow Angular frontend
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAngularApp", policy =>
    {
        policy.WithOrigins("http://localhost:4200", "https://localhost:4200")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "IFC Production Management API",
        Version = "v1",
        Description = "Enhanced production management system with recursive recipe processing"
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "IFC Production Management API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}

app.UseHttpsRedirection();

// Enable CORS
app.UseCors("AllowAngularApp");

app.UseAuthorization();

app.MapControllers();

// Ensure database is created (for development)
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<IFCDbContext>();
    try
    {
        // Only create if database doesn't exist - preserves existing VB.NET data
        context.Database.EnsureCreated();
    }
    catch (Exception ex)
    {
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while ensuring the database was created.");
    }
}

app.Run();
