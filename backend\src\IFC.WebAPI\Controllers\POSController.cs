using Microsoft.AspNetCore.Mvc;
using IFC.Application.Interfaces;
using IFC.Domain.Entities.POS;

namespace IFC.WebAPI.Controllers;

/// <summary>
/// POS Controller - Handles Point of Sale operations
/// Replicates VB.NET POS database operations exactly
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class POSController : ControllerBase
{
    private readonly IPOSRepository _posRepository;
    private readonly ILogger<POSController> _logger;

    public POSController(IPOSRepository posRepository, ILogger<POSController> logger)
    {
        _posRepository = posRepository;
        _logger = logger;
    }

    /// <summary>
    /// Gets all POS settings
    /// Replicates VB.NET: "Select * from POSSetting Order By Company_NamePOS"
    /// </summary>
    [HttpGet("settings")]
    public async Task<ActionResult<IEnumerable<POSSetting>>> GetPOSSettings()
    {
        try
        {
            var settings = await _posRepository.GetPOSSettingsAsync();
            return Ok(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting POS settings");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Gets POS settings by cost center
    /// Replicates VB.NET: "select * from POSSetting Where CostCenter_IdPOS=" & CostCenter_IdPOS
    /// </summary>
    [HttpGet("settings/costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<POSSetting>>> GetPOSSettingsByCostCenter(int costCenterId)
    {
        try
        {
            var settings = await _posRepository.GetPOSSettingsByCostCenterAsync(costCenterId);
            return Ok(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting POS settings for cost center {CostCenterId}", costCenterId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Loads POS transaction data for date range
    /// Replicates VB.NET: LoadPosData function
    /// </summary>
    [HttpGet("transactions")]
    public async Task<ActionResult<IEnumerable<POSTransaction>>> LoadPOSData(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] int costCenterId)
    {
        try
        {
            _logger.LogInformation("Loading POS data from {FromDate} to {ToDate} for cost center {CostCenterId}", 
                fromDate, toDate, costCenterId);

            var transactions = await _posRepository.LoadPOSDataAsync(fromDate, toDate, costCenterId);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading POS data");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Loads POS data online (unprocessed transactions)
    /// Replicates VB.NET: LoadPosDataOnLine function
    /// </summary>
    [HttpGet("transactions/online")]
    public async Task<ActionResult<IEnumerable<POSTransaction>>> LoadPOSDataOnline(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate)
    {
        try
        {
            var transactions = await _posRepository.LoadPOSDataOnlineAsync(fromDate, toDate);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading POS data online");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Gets unprocessed POS transactions
    /// </summary>
    [HttpGet("transactions/unprocessed")]
    public async Task<ActionResult<IEnumerable<POSTransaction>>> GetUnprocessedTransactions()
    {
        try
        {
            var transactions = await _posRepository.GetUnprocessedTransactionsAsync();
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unprocessed transactions");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Marks transaction as processed
    /// Replicates VB.NET: UpdateCHDIDOnline function
    /// </summary>
    [HttpPost("transactions/{chDID}/mark-processed")]
    public async Task<ActionResult<bool>> MarkTransactionAsProcessed(int chDID)
    {
        try
        {
            var result = await _posRepository.UpdateTransactionAsProcessedAsync(chDID);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking transaction {ChDID} as processed", chDID);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Ensures v_ProcIFC view exists
    /// Replicates VB.NET: CheckExistV_ProcIFC function
    /// </summary>
    [HttpPost("ensure-view")]
    public async Task<ActionResult<bool>> EnsureViewExists()
    {
        try
        {
            var result = await _posRepository.EnsureViewExistsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring view exists");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Drops v_ProcIFC view if it exists
    /// Replicates VB.NET: DeleteViewIFC function
    /// </summary>
    [HttpPost("drop-view")]
    public async Task<ActionResult<bool>> DropViewIfExists()
    {
        try
        {
            var result = await _posRepository.DropViewIfExistsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dropping view");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Gets sales data by date range
    /// </summary>
    [HttpGet("sales")]
    public async Task<ActionResult<IEnumerable<SalesPOS>>> GetSalesData(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] int? costCenterId = null)
    {
        try
        {
            var salesData = await _posRepository.GetSalesDataAsync(fromDate, toDate, costCenterId);
            return Ok(salesData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sales data");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Tests POS database connection
    /// Replicates VB.NET: TestConnectionPOS function
    /// </summary>
    [HttpGet("test-connection")]
    public async Task<ActionResult<bool>> TestConnection()
    {
        try
        {
            var result = await _posRepository.TestConnectionAsync();
            return Ok(new { connected = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing POS connection");
            return Ok(new { connected = false, error = ex.Message });
        }
    }

    /// <summary>
    /// Executes custom POS SQL command
    /// Replicates VB.NET: EXECUT_TxtPOS function
    /// </summary>
    [HttpPost("execute-command")]
    public async Task<ActionResult<bool>> ExecuteCommand([FromBody] string sqlCommand)
    {
        try
        {
            var result = await _posRepository.ExecutePOSCommandAsync(sqlCommand);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing POS command");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}
