export interface ProductionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  materialIssues: MaterialAvailabilityIssue[];
  dependencyIssues: DependencyIssue[];
  estimatedCost: number;
  estimatedDurationMinutes: number;
  earliestStartDate: Date;
  estimatedCompletionDate: Date;
}

export interface MaterialAvailabilityIssue {
  productId: number;
  productCode: string;
  productName: string;
  requiredQuantity: number;
  availableQuantity: number;
  shortageQuantity: number;
  unit: string;
  costCenterId: number;
  costCenterName: string;
  issueType: string;
  suggestion?: string;
}

export interface DependencyIssue {
  productId: number;
  productCode: string;
  productName: string;
  issueType: string;
  description: string;
  dependencyChain: number[];
}

export interface ProductionHierarchy {
  productId: number;
  productCode: string;
  productName: string;
  quantity: number;
  unit: string;
  hierarchyLevel: number;
  isRecipe: boolean;
  isProduction: boolean;
  estimatedCost: number;
  estimatedDurationMinutes: number;
  children: ProductionHierarchy[];
  directMaterials: MaterialRequirement[];
}

export interface MaterialRequirements {
  productId: number;
  productCode: string;
  productName: string;
  quantity: number;
  requirements: MaterialRequirement[];
  totalEstimatedCost: number;
}

export interface MaterialRequirement {
  productId: number;
  productCode: string;
  productName: string;
  requiredQuantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  hierarchyLevel: number;
  isRawMaterial: boolean;
  requiresProduction: boolean;
  parentProductId?: number;
  parentProductCode?: string;
}

export interface MaterialReservationResult {
  isSuccessful: boolean;
  errors: string[];
  reservations: MaterialReservation[];
  totalReservedValue: number;
}

export interface MaterialReservation {
  productId: number;
  productCode: string;
  reservedQuantity: number;
  unit: string;
  costCenterId: number;
  batchId?: number;
  batchNumber?: string;
  reservationDate: Date;
  unitCost: number;
  totalValue: number;
}

export interface ProductionPlan {
  productId: number;
  productCode: string;
  productName: string;
  quantity: number;
  steps: ProductionPlanStep[];
  totalDurationMinutes: number;
  totalCost: number;
}

export interface ProductionPlanStep {
  stepNumber: number;
  productId: number;
  productCode: string;
  productName: string;
  quantity: number;
  stepType: string;
  durationMinutes: number;
  estimatedCost: number;
  dependencies: number[];
  hierarchyLevel: number;
}

export interface DependencyValidationResult {
  isValid: boolean;
  errors: string[];
  issues: DependencyIssue[];
  hasCircularDependency: boolean;
  circularDependencyChain: number[];
  maxDepth: number;
}

export interface ProductionSequenceStep {
  sequenceNumber: number;
  productId: number;
  productCode: string;
  productName: string;
  quantity: number;
  unit: string;
  hierarchyLevel: number;
  plannedStartDate: Date;
  plannedEndDate: Date;
  prerequisites: number[];
  canRunInParallel: boolean;
}

export interface ProductionStatistics {
  totalOrders: number;
  completedOrders: number;
  inProgressOrders: number;
  overdueOrders: number;
  totalQuantityProduced: number;
  totalProductionCost: number;
  averageCompletionTime: number;
  onTimeDeliveryPercentage: number;
}
