using Microsoft.AspNetCore.Mvc;
using IFC.Application.Interfaces;
using IFC.Domain.Models;
using IFC.WebAPI.DTOs;
using AutoMapper;

namespace IFC.WebAPI.Controllers;

/// <summary>
/// Production Controller - Handles production orders and recursive recipe processing
/// Replicates the exact VB.NET functionality but with enhanced recursive processing
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProductionController : ControllerBase
{
    private readonly IProductionService _productionService;
    private readonly IMapper _mapper;
    private readonly ILogger<ProductionController> _logger;

    public ProductionController(
        IProductionService productionService,
        IMapper mapper,
        ILogger<ProductionController> logger)
    {
        _productionService = productionService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Creates a new production order with recursive processing
    /// Replicates VB.NET: GetProductionRecipyData() + RowEffectQuantity() but with recursion
    /// </summary>
    [HttpPost("orders")]
    public async Task<ActionResult<ProductionOrderDto>> CreateProductionOrder([FromBody] CreateProductionOrderDto request)
    {
        try
        {
            _logger.LogInformation("Creating production order for Product {ProductId}, Quantity {Quantity}", 
                request.ProductId, request.Quantity);

            var productionOrder = await _productionService.CreateProductionOrderAsync(
                request.ProductId,
                request.Quantity,
                request.CostCenterId,
                request.PlannedStartDate,
                request.Priority);

            var result = _mapper.Map<ProductionOrderDto>(productionOrder);
            return CreatedAtAction(nameof(GetProductionOrder), new { id = result.ProductionOrderId }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating production order");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Validates a production order before creation
    /// Replicates VB.NET: RowEffectQuantity() validation but with recursive checking
    /// </summary>
    [HttpPost("orders/validate")]
    public async Task<ActionResult<ProductionValidationResultDto>> ValidateProductionOrder([FromBody] ValidateProductionOrderDto request)
    {
        try
        {
            _logger.LogInformation("Validating production order for Product {ProductId}, Quantity {Quantity}", 
                request.ProductId, request.Quantity);

            var validationResult = await _productionService.ValidateProductionOrderAsync(
                request.ProductId,
                request.Quantity,
                request.CostCenterId);

            var result = _mapper.Map<ProductionValidationResultDto>(validationResult);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating production order");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Gets production hierarchy for a product
    /// Shows the complete nested recipe structure like VB.NET DGVRecipe grid
    /// </summary>
    [HttpGet("hierarchy/{productId}")]
    public async Task<ActionResult<ProductionHierarchyDto>> GetProductionHierarchy(int productId, [FromQuery] decimal quantity = 1)
    {
        try
        {
            var hierarchy = await _productionService.GetProductionHierarchyAsync(productId, quantity);
            var result = _mapper.Map<ProductionHierarchyDto>(hierarchy);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting production hierarchy");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Calculates material requirements recursively
    /// Replicates VB.NET recipe ingredient calculation but with full recursion
    /// </summary>
    [HttpGet("materials/{productId}")]
    public async Task<ActionResult<MaterialRequirementsDto>> GetMaterialRequirements(int productId, [FromQuery] decimal quantity = 1)
    {
        try
        {
            var requirements = await _productionService.CalculateMaterialRequirementsAsync(productId, quantity);
            var result = _mapper.Map<MaterialRequirementsDto>(requirements);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating material requirements");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Gets a production order with full hierarchy
    /// </summary>
    [HttpGet("orders/{id}")]
    public async Task<ActionResult<ProductionOrderDto>> GetProductionOrder(int id)
    {
        try
        {
            var productionOrder = await _productionService.GetProductionOrderWithHierarchyAsync(id);
            if (productionOrder == null)
                return NotFound();

            var result = _mapper.Map<ProductionOrderDto>(productionOrder);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting production order {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Gets production orders by status
    /// Replicates VB.NET production order filtering
    /// </summary>
    [HttpGet("orders")]
    public async Task<ActionResult<IEnumerable<ProductionOrderDto>>> GetProductionOrders(
        [FromQuery] int? status = null,
        [FromQuery] int? costCenterId = null)
    {
        try
        {
            var productionOrders = await _productionService.GetProductionOrdersByStatusAsync(
                (Domain.Enums.ProductionOrderStatus?)status ?? Domain.Enums.ProductionOrderStatus.Planned,
                costCenterId);

            var result = _mapper.Map<IEnumerable<ProductionOrderDto>>(productionOrders);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting production orders");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Starts production for an order
    /// Replicates VB.NET production start process
    /// </summary>
    [HttpPost("orders/{id}/start")]
    public async Task<ActionResult<ProductionOrderDto>> StartProduction(int id, [FromBody] StartProductionDto request)
    {
        try
        {
            var productionOrder = await _productionService.StartProductionAsync(id, request.UserId);
            var result = _mapper.Map<ProductionOrderDto>(productionOrder);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting production order {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Completes production for an order
    /// </summary>
    [HttpPost("orders/{id}/complete")]
    public async Task<ActionResult<ProductionOrderDto>> CompleteProduction(int id, [FromBody] CompleteProductionDto request)
    {
        try
        {
            var productionOrder = await _productionService.CompleteProductionAsync(
                id, request.ActualQuantityProduced, request.UserId);
            var result = _mapper.Map<ProductionOrderDto>(productionOrder);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing production order {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Cancels a production order
    /// </summary>
    [HttpPost("orders/{id}/cancel")]
    public async Task<ActionResult<ProductionOrderDto>> CancelProduction(int id, [FromBody] CancelProductionDto request)
    {
        try
        {
            var productionOrder = await _productionService.CancelProductionAsync(
                id, request.Reason, request.UserId);
            var result = _mapper.Map<ProductionOrderDto>(productionOrder);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling production order {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Reserves materials for production
    /// Replicates VB.NET inventory reservation logic
    /// </summary>
    [HttpPost("orders/{id}/reserve-materials")]
    public async Task<ActionResult<MaterialReservationResultDto>> ReserveMaterials(int id)
    {
        try
        {
            var result = await _productionService.ReserveMaterialsAsync(id);
            var dto = _mapper.Map<MaterialReservationResultDto>(result);
            return Ok(dto);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reserving materials for production order {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Releases reserved materials
    /// </summary>
    [HttpPost("orders/{id}/release-materials")]
    public async Task<ActionResult<MaterialReservationResultDto>> ReleaseMaterials(int id)
    {
        try
        {
            var result = await _productionService.ReleaseMaterialsAsync(id);
            var dto = _mapper.Map<MaterialReservationResultDto>(result);
            return Ok(dto);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing materials for production order {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}
