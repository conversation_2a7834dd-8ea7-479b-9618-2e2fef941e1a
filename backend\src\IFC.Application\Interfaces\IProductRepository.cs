using IFC.Domain.Entities;

namespace IFC.Application.Interfaces;

/// <summary>
/// Repository interface for Product entity
/// </summary>
public interface IProductRepository
{
    /// <summary>
    /// Gets a product by ID
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Product or null if not found</returns>
    Task<Product?> GetByIdAsync(int id);
    
    /// <summary>
    /// Gets a product by ID with recipe information loaded
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Product with recipe or null if not found</returns>
    Task<Product?> GetByIdWithRecipeAsync(int id);
    
    /// <summary>
    /// Gets a product by product code
    /// </summary>
    /// <param name="productCode">Product code</param>
    /// <returns>Product or null if not found</returns>
    Task<Product?> GetByCodeAsync(string productCode);
    
    /// <summary>
    /// Gets all products
    /// </summary>
    /// <returns>List of all products</returns>
    Task<IEnumerable<Product>> GetAllAsync();
    
    /// <summary>
    /// Gets products by type (recipe, production, sales, etc.)
    /// </summary>
    /// <param name="isRecipe">Filter by recipe products</param>
    /// <param name="isProduction">Filter by production products</param>
    /// <param name="isSales">Filter by sales products</param>
    /// <returns>Filtered list of products</returns>
    Task<IEnumerable<Product>> GetByTypeAsync(bool? isRecipe = null, bool? isProduction = null, bool? isSales = null);
    
    /// <summary>
    /// Searches products by name or code
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <returns>Matching products</returns>
    Task<IEnumerable<Product>> SearchAsync(string searchTerm);
    
    /// <summary>
    /// Gets products with low inventory
    /// </summary>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>Products below minimum stock</returns>
    Task<IEnumerable<Product>> GetLowInventoryProductsAsync(int? costCenterId = null);
    
    /// <summary>
    /// Adds a new product
    /// </summary>
    /// <param name="product">Product to add</param>
    /// <returns>Added product with ID</returns>
    Task<Product> AddAsync(Product product);
    
    /// <summary>
    /// Updates an existing product
    /// </summary>
    /// <param name="product">Product to update</param>
    /// <returns>Updated product</returns>
    Task<Product> UpdateAsync(Product product);
    
    /// <summary>
    /// Deletes a product (soft delete by setting IsActive = false)
    /// </summary>
    /// <param name="id">Product ID to delete</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteAsync(int id);
    
    /// <summary>
    /// Checks if a product code already exists
    /// </summary>
    /// <param name="productCode">Product code to check</param>
    /// <param name="excludeId">Product ID to exclude from check (for updates)</param>
    /// <returns>True if code exists</returns>
    Task<bool> ExistsAsync(string productCode, int? excludeId = null);
}
