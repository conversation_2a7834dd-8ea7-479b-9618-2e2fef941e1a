# IFC Production Management System - Web Migration

## 🎯 Project Overview

This project migrates the existing VB.NET desktop production system to a modern web-based application using **.NET 8 Web API** and **Angular 17 standalone components**. The migration fixes the core issue with nested recipe processing while maintaining the exact same business logic and database structure.

## 🐞 Problem Solved

### **Original VB.NET Issue:**
The desktop system failed when processing nested recipes:

```
Product 12501 (qty: 4) contains:
├── 500012 (raw material) ✅ Works fine
└── 12508 (recipe item) ❌ FAILS - System can't handle nested recipes
    └── 12508 internally uses 500011
```

**Root Cause:** The `RowEffectQuantity()` function in VB.NET only checked immediate ingredients, not recursive dependencies.

### **New Web Solution:**
The web system handles recursive processing automatically:

```
Product 12501 (qty: 4) contains:
├── 500012 (raw material) ✅ Check inventory
└── 12508 (recipe item) ✅ AUTO-PROCESS RECURSIVELY
    ├── Validate 12508 can be produced
    ├── Check 500011 availability for 12508
    ├── Create sub-production order for 12508
    ├── Schedule 12508 production first
    └── Continue with 12501 production
```

## 🚀 Migration to Modern Architecture

**Current State:** VB.NET Desktop Application
**Target State:** .NET 8 Web API + Angular 17 Standalone Components

### Key Features
- ✅ Recursive production processing for nested recipes
- ✅ Multi-level inventory validation
- ✅ Circular dependency detection
- ✅ Real-time production status tracking
- ✅ Modern web-based UI with tree-structured recipe editor

## 🏗️ Architecture Overview

### Backend (.NET 8 Web API)
- **Domain Models:** Product, Recipe, ProductionOrder, Inventory
- **Services:** RecursiveProductionProcessor, InventoryValidator
- **Controllers:** Products, Recipes, Production, Inventory

### Frontend (Angular 17)
- **Components:** Recipe Editor, Production Dashboard, Inventory Management
- **Services:** Production, Recipe, Inventory services
- **Features:** Standalone components, reactive forms, real-time updates

## 🔧 Current System Issues (Legacy VB.NET)

### Production Logic Problems
1. **No Recursive Processing:** System fails when recipes contain other recipes
2. **Limited Validation:** Only checks immediate ingredients, not sub-recipe requirements
3. **No Circular Detection:** Can create infinite loops in recipe dependencies
4. **Manual Processing:** Requires manual intervention for nested production

### Example Scenario (Current Failure)
```
Product 12501 (qty: 4) contains:
├── 500012 (raw material) ✅ Works
└── 12508 (recipe item) ❌ Fails
    └── 500011 (raw material needed for 12508)
```

## ✅ Enhanced Solution Features

### Recursive Production Engine
- **Multi-Level Processing:** Automatically handles recipes within recipes
- **Dependency Resolution:** Resolves all sub-recipe requirements
- **Inventory Validation:** Checks availability across all hierarchy levels
- **Cost Calculation:** Accurate costing through all production levels

### Example Scenario (New Solution)
```
Product 12501 (qty: 4) contains:
├── 500012 (raw material) ✅ Check inventory
└── 12508 (recipe item) ✅ Auto-produce
    ├── Check if 12508 can be produced
    ├── Validate 500011 availability
    ├── Schedule 12508 production first
    └── Continue with 12501 production
```

## 🛠️ Technology Stack

### Backend
- **.NET 8 Web API**
- **Entity Framework Core**
- **SQL Server**
- **AutoMapper**
- **FluentValidation**

### Frontend
- **Angular 17**
- **Standalone Components**
- **Angular Material**
- **RxJS**
- **TypeScript**

## 📁 Project Structure

```
/backend
├── /src
│   ├── /IFC.Domain          # Domain models and interfaces
│   ├── /IFC.Application     # Business logic and services
│   ├── /IFC.Infrastructure  # Data access and external services
│   └── /IFC.WebAPI          # API controllers and configuration
└── /tests
    ├── /IFC.UnitTests
    └── /IFC.IntegrationTests

/frontend
├── /src
│   ├── /app
│   │   ├── /components      # Standalone components
│   │   ├── /services        # Angular services
│   │   ├── /models          # TypeScript interfaces
│   │   └── /shared          # Shared utilities
│   └── /assets
└── /tests
```

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- Node.js 18+
- SQL Server
- Angular CLI 17+

### Backend Setup
```bash
cd backend/src/IFC.WebAPI
dotnet restore
dotnet run
```

### Frontend Setup
```bash
cd frontend
npm install
ng serve
```

## 📊 Database Schema

### Key Tables
- **Products:** Product catalog with recipe/production flags
- **Recipes:** Recipe definitions and hierarchies
- **RecipeIngredients:** Components and quantities
- **ProductionOrders:** Production requests and status
- **Inventory:** Stock levels and movements

## 🧪 Testing Strategy

### Unit Tests
- Recipe validation logic
- Circular dependency detection
- Inventory calculation algorithms
- Production scheduling logic

### Integration Tests
- End-to-end production workflows
- Database operations
- API endpoint testing
- Frontend component testing

## 📈 Performance Considerations

- **Caching:** Recipe hierarchies and inventory data
- **Async Processing:** Long-running production calculations
- **Pagination:** Large product catalogs
- **Real-time Updates:** WebSocket connections for live status

## 🔒 Security Features

- **Authentication:** JWT-based user authentication
- **Authorization:** Role-based access control
- **Validation:** Input sanitization and validation
- **Audit Trail:** Production and inventory change tracking

## 📝 API Documentation

API documentation will be available at `/swagger` when running the backend application.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
