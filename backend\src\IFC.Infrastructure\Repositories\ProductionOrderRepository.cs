using Microsoft.EntityFrameworkCore;
using IFC.Application.Interfaces;
using IFC.Domain.Entities;
using IFC.Domain.Enums;
using IFC.Infrastructure.Data;

namespace IFC.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for ProductionOrder entity
/// </summary>
public class ProductionOrderRepository : IProductionOrderRepository
{
    private readonly IFCDbContext _context;

    public ProductionOrderRepository(IFCDbContext context)
    {
        _context = context;
    }

    public async Task<ProductionOrder?> GetByIdAsync(int id)
    {
        return await _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .FirstOrDefaultAsync(p => p.ProductionOrderId == id);
    }

    public async Task<ProductionOrder?> GetByIdWithHierarchyAsync(int id)
    {
        return await _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Include(p => p.ChildProductionOrders)
                .ThenInclude(c => c.Product)
            .Include(p => p.ParentProductionOrder)
            .FirstOrDefaultAsync(p => p.ProductionOrderId == id);
    }

    public async Task<ProductionOrder?> GetByOrderNumberAsync(string orderNumber)
    {
        return await _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .FirstOrDefaultAsync(p => p.OrderNumber == orderNumber);
    }

    public async Task<IEnumerable<ProductionOrder>> GetByStatusAsync(ProductionOrderStatus status, int? costCenterId = null)
    {
        var query = _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Where(p => p.Status == status);

        if (costCenterId.HasValue)
            query = query.Where(p => p.CostCenterId == costCenterId.Value);

        return await query.OrderBy(p => p.PlannedStartDate).ToListAsync();
    }

    public async Task<IEnumerable<ProductionOrder>> GetByProductIdAsync(int productId, bool includeCompleted = false)
    {
        var query = _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Where(p => p.ProductId == productId);

        if (!includeCompleted)
            query = query.Where(p => p.Status != ProductionOrderStatus.Completed);

        return await query.OrderByDescending(p => p.CreatedDate).ToListAsync();
    }

    public async Task<IEnumerable<ProductionOrder>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, int? costCenterId = null)
    {
        var query = _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Where(p => p.PlannedStartDate >= fromDate && p.PlannedStartDate <= toDate);

        if (costCenterId.HasValue)
            query = query.Where(p => p.CostCenterId == costCenterId.Value);

        return await query.OrderBy(p => p.PlannedStartDate).ToListAsync();
    }

    public async Task<IEnumerable<ProductionOrder>> GetChildOrdersAsync(int parentOrderId)
    {
        return await _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Where(p => p.ParentProductionOrderId == parentOrderId)
            .OrderBy(p => p.HierarchyLevel)
            .ThenBy(p => p.PlannedStartDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<ProductionOrder>> GetTopLevelOrdersAsync(int? costCenterId = null)
    {
        var query = _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Where(p => p.ParentProductionOrderId == null);

        if (costCenterId.HasValue)
            query = query.Where(p => p.CostCenterId == costCenterId.Value);

        return await query.OrderByDescending(p => p.CreatedDate).ToListAsync();
    }

    public async Task<IEnumerable<ProductionOrder>> GetReadyToStartAsync(int? costCenterId = null)
    {
        var query = _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Where(p => p.Status == ProductionOrderStatus.ReadyToStart);

        if (costCenterId.HasValue)
            query = query.Where(p => p.CostCenterId == costCenterId.Value);

        return await query.OrderBy(p => p.Priority).ThenBy(p => p.PlannedStartDate).ToListAsync();
    }

    public async Task<IEnumerable<ProductionOrder>> GetOverdueOrdersAsync(int? costCenterId = null)
    {
        var now = DateTime.UtcNow;
        var query = _context.ProductionOrders
            .Include(p => p.Product)
            .Include(p => p.Recipe)
            .Where(p => p.PlannedEndDate < now && 
                       p.Status != ProductionOrderStatus.Completed && 
                       p.Status != ProductionOrderStatus.Cancelled);

        if (costCenterId.HasValue)
            query = query.Where(p => p.CostCenterId == costCenterId.Value);

        return await query.OrderBy(p => p.PlannedEndDate).ToListAsync();
    }

    public async Task<ProductionOrder?> GetLastOrderAsync()
    {
        return await _context.ProductionOrders
            .OrderByDescending(p => p.ProductionOrderId)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<ProductionOrder>> GetOrdersWithMaterialShortagesAsync(int? costCenterId = null)
    {
        // This would require complex logic to check material availability
        // For now, return empty list - can be implemented later
        return new List<ProductionOrder>();
    }

    public async Task<ProductionOrder> AddAsync(ProductionOrder productionOrder)
    {
        _context.ProductionOrders.Add(productionOrder);
        await _context.SaveChangesAsync();
        return productionOrder;
    }

    public async Task<ProductionOrder> UpdateAsync(ProductionOrder productionOrder)
    {
        _context.ProductionOrders.Update(productionOrder);
        await _context.SaveChangesAsync();
        return productionOrder;
    }

    public async Task<bool> UpdateStatusAsync(int id, ProductionOrderStatus status, int userId)
    {
        var order = await _context.ProductionOrders.FindAsync(id);
        if (order == null) return false;

        order.Status = status;
        order.ModifiedDate = DateTime.UtcNow;
        order.ModifiedBy = userId;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var order = await _context.ProductionOrders.FindAsync(id);
        if (order == null) return false;

        // Soft delete by setting status to cancelled
        order.Status = ProductionOrderStatus.Cancelled;
        order.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<ProductionStatistics> GetProductionStatisticsAsync(DateTime fromDate, DateTime toDate, int? costCenterId = null)
    {
        var query = _context.ProductionOrders.AsQueryable();

        if (costCenterId.HasValue)
            query = query.Where(p => p.CostCenterId == costCenterId.Value);

        query = query.Where(p => p.CreatedDate >= fromDate && p.CreatedDate <= toDate);

        var orders = await query.ToListAsync();

        return new ProductionStatistics
        {
            TotalOrders = orders.Count,
            CompletedOrders = orders.Count(o => o.Status == ProductionOrderStatus.Completed),
            InProgressOrders = orders.Count(o => o.Status == ProductionOrderStatus.InProgress),
            OverdueOrders = orders.Count(o => o.PlannedEndDate < DateTime.UtcNow && 
                                             o.Status != ProductionOrderStatus.Completed && 
                                             o.Status != ProductionOrderStatus.Cancelled),
            TotalQuantityProduced = orders.Where(o => o.Status == ProductionOrderStatus.Completed).Sum(o => o.QuantityProduced),
            TotalProductionCost = orders.Sum(o => o.EstimatedCost),
            AverageCompletionTime = 0, // Would need to calculate based on actual vs planned times
            OnTimeDeliveryPercentage = 0 // Would need to calculate based on delivery dates
        };
    }
}
