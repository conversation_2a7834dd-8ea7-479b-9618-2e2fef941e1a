using IFC.Domain.Entities;
using IFC.Domain.Enums;
using IFC.Domain.Interfaces;
using IFC.Domain.Models;
using IFC.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace IFC.Application.Services;

/// <summary>
/// Core recursive production processing engine
/// This is the heart of the enhanced production system that handles nested recipes
/// </summary>
public class RecursiveProductionProcessor : IRecursiveProductionProcessor
{
    private readonly ILogger<RecursiveProductionProcessor> _logger;
    private readonly IProductRepository _productRepository;
    private readonly IRecipeRepository _recipeRepository;
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IProductionOrderRepository _productionOrderRepository;

    public RecursiveProductionProcessor(
        ILogger<RecursiveProductionProcessor> logger,
        IProductRepository productRepository,
        IRecipeRepository recipeRepository,
        IInventoryRepository inventoryRepository,
        IProductionOrderRepository productionOrderRepository)
    {
        _logger = logger;
        _productRepository = productRepository;
        _recipeRepository = recipeRepository;
        _inventoryRepository = inventoryRepository;
        _productionOrderRepository = productionOrderRepository;
    }

    public async Task<ProductionOrder> ProcessRecursiveProductionAsync(
        int productId,
        decimal quantity,
        int costCenterId,
        int? parentOrderId = null,
        int hierarchyLevel = 0)
    {
        _logger.LogInformation("Starting recursive production processing for Product {ProductId}, Quantity {Quantity}, Level {Level}",
            productId, quantity, hierarchyLevel);

        // Get product and validate
        var product = await _productRepository.GetByIdAsync(productId);
        if (product == null)
            throw new ArgumentException($"Product with ID {productId} not found");

        // Check for circular dependencies
        if (await HasCircularDependencyAsync(productId))
            throw new InvalidOperationException($"Circular dependency detected for product {product.ProductCode}");

        // Create the main production order
        var productionOrder = new ProductionOrder
        {
            OrderNumber = await GenerateOrderNumberAsync(),
            ProductId = productId,
            QuantityToProduce = quantity,
            Unit = product.UnitName,
            CostCenterId = costCenterId,
            ParentProductionOrderId = parentOrderId,
            HierarchyLevel = hierarchyLevel,
            IsAutoGenerated = parentOrderId.HasValue,
            Status = ProductionOrderStatus.Planned,
            PlannedStartDate = DateTime.UtcNow,
            CreatedBy = 1 // TODO: Get from current user context
        };

        // If this product has a recipe, process it recursively
        if (product.IsRecipe && product.Recipe != null)
        {
            productionOrder.RecipeId = product.Recipe.RecipeId;

            // Calculate estimated cost and time
            productionOrder.EstimatedCost = product.Recipe.TotalRecipeCost * quantity;
            productionOrder.PlannedEndDate = productionOrder.PlannedStartDate
                .AddMinutes(product.Recipe.EstimatedProductionTimeMinutes);

            // Process each ingredient in the recipe
            foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.IsActive))
            {
                var requiredQuantity = ingredient.RequiredQuantity * quantity;

                // If ingredient is also a recipe or production item, create sub-production order
                if (ingredient.RequiresProduction)
                {
                    _logger.LogInformation("Creating sub-production for ingredient {ProductCode}, Quantity {Quantity}",
                        ingredient.IngredientProduct.ProductCode, requiredQuantity);

                    var subProductionOrder = await ProcessRecursiveProductionAsync(
                        ingredient.IngredientProductId,
                        requiredQuantity,
                        costCenterId,
                        productionOrder.ProductionOrderId,
                        hierarchyLevel + 1);

                    productionOrder.ChildProductionOrders.Add(subProductionOrder);
                }
            }
        }
        else if (product.IsProduction)
        {
            // This is a production item but doesn't have a recipe defined
            // This might be a legacy scenario or a simple production item
            productionOrder.EstimatedCost = product.CostPerUnit * quantity;
            productionOrder.PlannedEndDate = productionOrder.PlannedStartDate.AddHours(1); // Default 1 hour
        }

        // Save the production order
        await _productionOrderRepository.AddAsync(productionOrder);

        _logger.LogInformation("Completed recursive production processing for Product {ProductId}, Created Order {OrderNumber}",
            productId, productionOrder.OrderNumber);

        return productionOrder;
    }

    public async Task<ProductionPlan> CreateProductionPlanAsync(int recipeId, decimal quantity)
    {
        var recipe = await _recipeRepository.GetByIdWithIngredientsAsync(recipeId);
        if (recipe == null)
            throw new ArgumentException($"Recipe with ID {recipeId} not found");

        var plan = new ProductionPlan
        {
            ProductId = recipe.ProductId,
            ProductCode = recipe.Product.ProductCode,
            ProductName = recipe.Product.ProductName,
            Quantity = quantity
        };

        var stepNumber = 1;
        stepNumber = await BuildProductionStepsRecursively(recipe, quantity, plan.Steps, stepNumber, 0);

        return plan;
    }

    public async Task<DependencyValidationResult> ValidateDependenciesAsync(int productId, decimal quantity, int costCenterId)
    {
        var result = new DependencyValidationResult { IsValid = true };

        // Check for circular dependencies
        if (await HasCircularDependencyAsync(productId))
        {
            result.IsValid = false;
            result.HasCircularDependency = true;
            result.Errors.Add($"Circular dependency detected for product {productId}");

            // Get the circular dependency chain
            result.CircularDependencyChain = await GetCircularDependencyChainAsync(productId);
        }

        // Check recipe depth
        result.MaxDepth = await GetRecipeDepthAsync(productId);
        if (result.MaxDepth > 10) // Configurable limit
        {
            result.Errors.Add($"Recipe nesting too deep ({result.MaxDepth} levels). Maximum allowed is 10.");
            result.IsValid = false;
        }

        // Validate material availability recursively
        var materialRequirements = await ResolveMaterialRequirementsRecursivelyAsync(productId, quantity);
        foreach (var requirement in materialRequirements.Where(r => r.IsRawMaterial))
        {
            var availability = await _inventoryRepository.GetAvailableQuantityAsync(requirement.ProductId, costCenterId);
            if (availability < requirement.RequiredQuantity)
            {
                result.Issues.Add(new DependencyIssue
                {
                    ProductId = requirement.ProductId,
                    ProductCode = requirement.ProductCode,
                    ProductName = requirement.ProductName,
                    IssueType = "InsufficientInventory",
                    Description = $"Required: {requirement.RequiredQuantity} {requirement.Unit}, Available: {availability} {requirement.Unit}"
                });
                result.IsValid = false;
            }
        }

        return result;
    }

    public async Task<int> CalculateTotalProductionTimeAsync(int productId, decimal quantity)
    {
        var product = await _productRepository.GetByIdWithRecipeAsync(productId);
        if (product?.Recipe == null)
            return 0;

        var totalTime = product.Recipe.EstimatedProductionTimeMinutes;

        // Add time for sub-recipes
        foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.RequiresProduction))
        {
            var subTime = await CalculateTotalProductionTimeAsync(
                ingredient.IngredientProductId,
                ingredient.RequiredQuantity * quantity);
            totalTime += subTime;
        }

        return totalTime;
    }

    public async Task<IEnumerable<ProductionSequenceStep>> GetOptimalProductionSequenceAsync(int productId, decimal quantity)
    {
        var steps = new List<ProductionSequenceStep>();
        var stepNumber = 1;

        await BuildProductionSequenceRecursively(productId, quantity, steps, stepNumber, 0, DateTime.UtcNow);

        // Sort by dependencies and optimize for parallel execution
        return OptimizeProductionSequence(steps);
    }

    public async Task<IEnumerable<MaterialRequirement>> ResolveMaterialRequirementsRecursivelyAsync(
        int productId,
        decimal quantity,
        Dictionary<int, decimal>? resolvedMaterials = null)
    {
        resolvedMaterials ??= new Dictionary<int, decimal>();
        var requirements = new List<MaterialRequirement>();

        // Prevent infinite recursion
        if (resolvedMaterials.ContainsKey(productId))
        {
            resolvedMaterials[productId] += quantity;
            return requirements;
        }

        resolvedMaterials[productId] = quantity;

        var product = await _productRepository.GetByIdWithRecipeAsync(productId);
        if (product?.Recipe == null)
        {
            // This is a raw material
            requirements.Add(new MaterialRequirement
            {
                ProductId = productId,
                ProductCode = product?.ProductCode ?? "",
                ProductName = product?.ProductName ?? "",
                RequiredQuantity = quantity,
                Unit = product?.UnitName ?? "",
                UnitCost = product?.CostPerUnit ?? 0,
                IsRawMaterial = true,
                RequiresProduction = false
            });
            return requirements;
        }

        // Process recipe ingredients
        foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.IsActive))
        {
            var requiredQuantity = ingredient.RequiredQuantity * quantity;

            if (ingredient.RequiresProduction)
            {
                // Recursively resolve sub-recipe materials
                var subRequirements = await ResolveMaterialRequirementsRecursivelyAsync(
                    ingredient.IngredientProductId,
                    requiredQuantity,
                    resolvedMaterials);
                requirements.AddRange(subRequirements);
            }
            else
            {
                // This is a raw material
                requirements.Add(new MaterialRequirement
                {
                    ProductId = ingredient.IngredientProductId,
                    ProductCode = ingredient.IngredientProduct.ProductCode,
                    ProductName = ingredient.IngredientProduct.ProductName,
                    RequiredQuantity = requiredQuantity,
                    Unit = ingredient.RequiredUnit,
                    UnitCost = ingredient.CostPerUnit,
                    IsRawMaterial = true,
                    RequiresProduction = false,
                    ParentProductId = productId,
                    ParentProductCode = product.ProductCode
                });
            }
        }

        return requirements;
    }

    public async Task<bool> HasCircularDependencyAsync(int productId, HashSet<int>? visitedProducts = null)
    {
        visitedProducts ??= new HashSet<int>();

        if (visitedProducts.Contains(productId))
            return true;

        visitedProducts.Add(productId);

        var product = await _productRepository.GetByIdWithRecipeAsync(productId);
        if (product?.Recipe == null)
            return false;

        foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.RequiresProduction))
        {
            if (await HasCircularDependencyAsync(ingredient.IngredientProductId, new HashSet<int>(visitedProducts)))
                return true;
        }

        return false;
    }

    public async Task<int> GetRecipeDepthAsync(int productId)
    {
        var product = await _productRepository.GetByIdWithRecipeAsync(productId);
        if (product?.Recipe == null)
            return 0;

        var maxDepth = 0;
        foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.RequiresProduction))
        {
            var depth = await GetRecipeDepthAsync(ingredient.IngredientProductId);
            maxDepth = Math.Max(maxDepth, depth);
        }

        return maxDepth + 1;
    }

    public async Task<IEnumerable<ProductionOrder>> ScheduleProductionOrdersAsync(
        IEnumerable<ProductionOrder> productionOrders,
        DateTime startDate)
    {
        var orders = productionOrders.ToList();
        var scheduledOrders = new List<ProductionOrder>();

        // Sort by hierarchy level (deepest first) and dependencies
        var sortedOrders = orders
            .OrderByDescending(o => o.HierarchyLevel)
            .ThenBy(o => o.ProductionOrderId)
            .ToList();

        var currentDate = startDate;

        foreach (var order in sortedOrders)
        {
            // Calculate start date based on dependencies
            var dependencyEndDate = GetLatestDependencyEndDate(order, scheduledOrders);
            var actualStartDate = dependencyEndDate > currentDate ? dependencyEndDate : currentDate;

            order.PlannedStartDate = actualStartDate;
            order.PlannedEndDate = actualStartDate.AddMinutes(order.Recipe?.EstimatedProductionTimeMinutes ?? 60);

            scheduledOrders.Add(order);
            currentDate = order.PlannedEndDate;
        }

        return scheduledOrders;
    }

    // Private helper methods

    private async Task<string> GenerateOrderNumberAsync()
    {
        var lastOrder = await _productionOrderRepository.GetLastOrderAsync();
        var nextNumber = (lastOrder?.ProductionOrderId ?? 0) + 1;
        return $"PO{DateTime.UtcNow:yyyyMM}{nextNumber:D6}";
    }

    private async Task<int> BuildProductionStepsRecursively(
        Recipe recipe,
        decimal quantity,
        List<ProductionPlanStep> steps,
        int stepNumber,
        int hierarchyLevel)
    {
        // Add steps for sub-recipes first (dependencies)
        foreach (var ingredient in recipe.Ingredients.Where(i => i.RequiresProduction))
        {
            var subRecipe = await _recipeRepository.GetByProductIdAsync(ingredient.IngredientProductId);
            if (subRecipe != null)
            {
                stepNumber = await BuildProductionStepsRecursively(
                    subRecipe,
                    ingredient.RequiredQuantity * quantity,
                    steps,
                    stepNumber,
                    hierarchyLevel + 1);
            }
        }

        // Add the main production step
        steps.Add(new ProductionPlanStep
        {
            StepNumber = stepNumber,
            ProductId = recipe.ProductId,
            ProductCode = recipe.Product.ProductCode,
            ProductName = recipe.Product.ProductName,
            Quantity = quantity,
            StepType = "Production",
            DurationMinutes = recipe.EstimatedProductionTimeMinutes,
            EstimatedCost = recipe.TotalRecipeCost * quantity,
            HierarchyLevel = hierarchyLevel
        });

        return stepNumber + 1;
    }

    private async Task<int> BuildProductionSequenceRecursively(
        int productId,
        decimal quantity,
        List<ProductionSequenceStep> steps,
        int stepNumber,
        int hierarchyLevel,
        DateTime baseDate)
    {
        var product = await _productRepository.GetByIdWithRecipeAsync(productId);
        if (product?.Recipe == null) return stepNumber;

        var prerequisites = new List<int>();

        // Process dependencies first
        foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.RequiresProduction))
        {
            var prereqStepNumber = stepNumber;
            stepNumber = await BuildProductionSequenceRecursively(
                ingredient.IngredientProductId,
                ingredient.RequiredQuantity * quantity,
                steps,
                stepNumber,
                hierarchyLevel + 1,
                baseDate);
            prerequisites.Add(prereqStepNumber);
        }

        // Add this production step
        steps.Add(new ProductionSequenceStep
        {
            SequenceNumber = stepNumber,
            ProductId = productId,
            ProductCode = product.ProductCode,
            ProductName = product.ProductName,
            Quantity = quantity,
            Unit = product.UnitName,
            HierarchyLevel = hierarchyLevel,
            Prerequisites = prerequisites,
            PlannedStartDate = baseDate,
            PlannedEndDate = baseDate.AddMinutes(product.Recipe?.EstimatedProductionTimeMinutes ?? 60),
            CanRunInParallel = prerequisites.Count == 0
        });

        return stepNumber + 1;
    }

    private static IEnumerable<ProductionSequenceStep> OptimizeProductionSequence(List<ProductionSequenceStep> steps)
    {
        // Sort by hierarchy level (deepest first) and prerequisites
        return steps
            .OrderByDescending(s => s.HierarchyLevel)
            .ThenBy(s => s.Prerequisites.Count)
            .ThenBy(s => s.SequenceNumber);
    }

    private static DateTime GetLatestDependencyEndDate(ProductionOrder order, List<ProductionOrder> scheduledOrders)
    {
        var latestDate = DateTime.MinValue;

        // Check parent order
        if (order.ParentProductionOrderId.HasValue)
        {
            var parent = scheduledOrders.FirstOrDefault(o => o.ProductionOrderId == order.ParentProductionOrderId);
            if (parent != null && parent.PlannedEndDate > latestDate)
                latestDate = parent.PlannedEndDate;
        }

        return latestDate == DateTime.MinValue ? DateTime.UtcNow : latestDate;
    }

    private async Task<List<int>> GetCircularDependencyChainAsync(int productId)
    {
        // Implementation to trace the circular dependency chain
        // This would involve traversing the recipe hierarchy and tracking the path
        var chain = new List<int>();
        var visited = new HashSet<int>();

        await TraceCircularDependency(productId, chain, visited);

        return chain;
    }

    private async Task<bool> TraceCircularDependency(int productId, List<int> chain, HashSet<int> visited)
    {
        if (visited.Contains(productId))
        {
            // Found the circular reference, add it to complete the chain
            chain.Add(productId);
            return true;
        }

        visited.Add(productId);
        chain.Add(productId);

        var product = await _productRepository.GetByIdWithRecipeAsync(productId);
        if (product?.Recipe != null)
        {
            foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.RequiresProduction))
            {
                if (await TraceCircularDependency(ingredient.IngredientProductId, chain, visited))
                    return true;
            }
        }

        chain.RemoveAt(chain.Count - 1);
        return false;
    }
}
