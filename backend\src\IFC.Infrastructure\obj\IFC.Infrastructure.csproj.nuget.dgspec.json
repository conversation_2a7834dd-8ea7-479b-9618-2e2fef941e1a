{"format": 1, "restore": {"F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Infrastructure\\IFC.Infrastructure.csproj": {}}, "projects": {"F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Application\\IFC.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Application\\IFC.Application.csproj", "projectName": "IFC.Application", "projectPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Application\\IFC.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\IFC.Domain.csproj": {"projectPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\IFC.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\IFC.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\IFC.Domain.csproj", "projectName": "IFC.Domain", "projectPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\IFC.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Infrastructure\\IFC.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Infrastructure\\IFC.Infrastructure.csproj", "projectName": "IFC.Infrastructure", "projectPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Infrastructure\\IFC.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Application\\IFC.Application.csproj": {"projectPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Application\\IFC.Application.csproj"}, "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\IFC.Domain.csproj": {"projectPath": "F:\\IFC_SCM_POS_5 - Copy\\IFC_SCM_POS\\IFC_SCM_POS\\backend\\src\\IFC.Domain\\IFC.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}