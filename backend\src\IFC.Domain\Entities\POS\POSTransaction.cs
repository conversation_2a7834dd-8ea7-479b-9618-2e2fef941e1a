using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IFC.Domain.Entities.POS;

/// <summary>
/// POS Transaction entity - maps to v_ProcIFC view and related POS tables
/// Replicates VB.NET: LoadPosData and LoadPosDataOnLine functions
/// </summary>
[Table("v_ProcIFC")]
public class POSTransaction
{
    [Key]
    public int Id { get; set; }

    [Column("mandant")]
    public int Mandant { get; set; }

    [Column("outlet")]
    public int Outlet { get; set; }

    [Column("center")]
    public int Center { get; set; }

    [Column("centername")]
    [MaxLength(200)]
    public string CenterName { get; set; } = string.Empty;

    [Column("plu")]
    public int PLU { get; set; }

    [Column("article")]
    [MaxLength(200)]
    public string Article { get; set; } = string.Empty;

    [Column("Qty", TypeName = "decimal(18,4)")]
    public decimal Quantity { get; set; }

    [Column("price", TypeName = "decimal(18,4)")]
    public decimal Price { get; set; }

    [Column("Amount", TypeName = "decimal(18,4)")]
    public decimal Amount { get; set; }

    [Column("discount", TypeName = "decimal(18,4)")]
    public decimal Discount { get; set; }

    [Column("taxamount", TypeName = "decimal(18,4)")]
    public decimal TaxAmount { get; set; }

    [Column("statistdate")]
    public DateTime StatistDate { get; set; }

    [Column("IsIFCDone")]
    public int IsIFCDone { get; set; }

    [Column("ChDID")]
    public int? ChDID { get; set; }

    // Additional properties for Matrix POS support
    [Column("payform")]
    public int? PayForm { get; set; }

    [Column("payformname")]
    [MaxLength(100)]
    public string? PayFormName { get; set; }

    [Column("billnum")]
    [MaxLength(50)]
    public string? BillNum { get; set; }
}
