using System.ComponentModel.DataAnnotations;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents a recipe definition for a product
/// </summary>
public class Recipe
{
    [Key]
    public int RecipeId { get; set; }
    
    /// <summary>
    /// The product that this recipe produces
    /// </summary>
    public int ProductId { get; set; }
    
    [Required]
    [StringLength(200)]
    public string RecipeName { get; set; } = string.Empty;
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    /// <summary>
    /// Version number for recipe versioning
    /// </summary>
    [StringLength(20)]
    public string Version { get; set; } = "1.0";
    
    /// <summary>
    /// Quantity that this recipe produces
    /// </summary>
    public decimal OutputQuantity { get; set; } = 1;
    
    /// <summary>
    /// Unit of measure for the output quantity
    /// </summary>
    [StringLength(50)]
    public string OutputUnit { get; set; } = string.Empty;
    
    /// <summary>
    /// Estimated production time in minutes
    /// </summary>
    public int EstimatedProductionTimeMinutes { get; set; }
    
    /// <summary>
    /// Labor cost for producing this recipe
    /// </summary>
    public decimal LaborCost { get; set; }
    
    /// <summary>
    /// Overhead cost for producing this recipe
    /// </summary>
    public decimal OverheadCost { get; set; }
    
    /// <summary>
    /// Indicates if this recipe is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Indicates if this is the default recipe for the product
    /// </summary>
    public bool IsDefault { get; set; } = true;
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    public int CreatedBy { get; set; }
    
    public int? ModifiedBy { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// The product that this recipe produces
    /// </summary>
    public virtual Product Product { get; set; } = null!;
    
    /// <summary>
    /// List of ingredients required for this recipe
    /// </summary>
    public virtual ICollection<RecipeIngredient> Ingredients { get; set; } = new List<RecipeIngredient>();
    
    /// <summary>
    /// Production steps for this recipe
    /// </summary>
    public virtual ICollection<ProductionStep> ProductionSteps { get; set; } = new List<ProductionStep>();
    
    // Calculated properties
    
    /// <summary>
    /// Total material cost calculated from ingredients
    /// </summary>
    public decimal TotalMaterialCost => Ingredients.Sum(i => i.TotalCost);
    
    /// <summary>
    /// Total recipe cost including materials, labor, and overhead
    /// </summary>
    public decimal TotalRecipeCost => TotalMaterialCost + LaborCost + OverheadCost;
    
    /// <summary>
    /// Cost per unit of output
    /// </summary>
    public decimal CostPerUnit => OutputQuantity > 0 ? TotalRecipeCost / OutputQuantity : 0;
    
    /// <summary>
    /// Checks if this recipe has any nested recipes (recipes as ingredients)
    /// </summary>
    public bool HasNestedRecipes => Ingredients.Any(i => i.IngredientProduct.IsRecipe);
    
    /// <summary>
    /// Gets the maximum depth of nested recipes
    /// </summary>
    public int GetRecipeDepth()
    {
        if (!HasNestedRecipes)
            return 1;
            
        var maxNestedDepth = Ingredients
            .Where(i => i.IngredientProduct.IsRecipe && i.IngredientProduct.Recipe != null)
            .Select(i => i.IngredientProduct.Recipe!.GetRecipeDepth())
            .DefaultIfEmpty(0)
            .Max();
            
        return maxNestedDepth + 1;
    }
}
