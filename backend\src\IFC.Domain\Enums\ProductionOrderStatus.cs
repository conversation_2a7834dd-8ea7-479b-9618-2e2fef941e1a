namespace IFC.Domain.Enums;

/// <summary>
/// Represents the status of a production order
/// </summary>
public enum ProductionOrderStatus
{
    /// <summary>
    /// Production order has been created but not yet started
    /// </summary>
    Planned = 1,
    
    /// <summary>
    /// Materials are being reserved and validated
    /// </summary>
    MaterialReservation = 2,
    
    /// <summary>
    /// Waiting for prerequisite production orders to complete
    /// </summary>
    WaitingForDependencies = 3,
    
    /// <summary>
    /// Ready to start production
    /// </summary>
    ReadyToStart = 4,
    
    /// <summary>
    /// Production is currently in progress
    /// </summary>
    InProgress = 5,
    
    /// <summary>
    /// Production has been temporarily paused
    /// </summary>
    Paused = 6,
    
    /// <summary>
    /// Production has been completed successfully
    /// </summary>
    Completed = 7,
    
    /// <summary>
    /// Production order has been cancelled
    /// </summary>
    Cancelled = 8,
    
    /// <summary>
    /// Production failed due to errors or insufficient materials
    /// </summary>
    Failed = 9,
    
    /// <summary>
    /// Production is on hold pending approval or resources
    /// </summary>
    OnHold = 10
}
