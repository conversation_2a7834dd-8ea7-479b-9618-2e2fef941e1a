using System.ComponentModel.DataAnnotations;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents the consumption of materials during production
/// </summary>
public class MaterialConsumption
{
    [Key]
    public int ConsumptionId { get; set; }
    
    /// <summary>
    /// Production order that consumed the material
    /// </summary>
    public int ProductionOrderId { get; set; }
    
    /// <summary>
    /// Recipe ingredient this consumption is for
    /// </summary>
    public int RecipeIngredientId { get; set; }
    
    /// <summary>
    /// Product that was consumed
    /// </summary>
    public int ProductId { get; set; }
    
    /// <summary>
    /// Cost center where consumption occurred
    /// </summary>
    public int CostCenterId { get; set; }
    
    /// <summary>
    /// Batch that was consumed (for expirable products)
    /// </summary>
    public int? BatchId { get; set; }
    
    /// <summary>
    /// Planned quantity to consume (from recipe)
    /// </summary>
    public decimal PlannedQuantity { get; set; }
    
    /// <summary>
    /// Actual quantity consumed
    /// </summary>
    public decimal ActualQuantity { get; set; }
    
    /// <summary>
    /// Unit of measure for quantities
    /// </summary>
    [StringLength(50)]
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Cost per unit at time of consumption
    /// </summary>
    public decimal UnitCost { get; set; }
    
    /// <summary>
    /// Total cost of consumption
    /// </summary>
    public decimal TotalCost => ActualQuantity * UnitCost;
    
    /// <summary>
    /// Variance between planned and actual quantity
    /// </summary>
    public decimal QuantityVariance => ActualQuantity - PlannedQuantity;
    
    /// <summary>
    /// Variance percentage
    /// </summary>
    public decimal VariancePercentage
    {
        get
        {
            if (PlannedQuantity == 0) return 0;
            return (QuantityVariance / PlannedQuantity) * 100;
        }
    }
    
    /// <summary>
    /// Date and time when consumption occurred
    /// </summary>
    public DateTime ConsumptionDate { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// User who recorded the consumption
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// Reason for any variance
    /// </summary>
    [StringLength(500)]
    public string? VarianceReason { get; set; }
    
    /// <summary>
    /// Notes about the consumption
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Indicates if this consumption has been posted to inventory
    /// </summary>
    public bool IsPosted { get; set; } = false;
    
    /// <summary>
    /// Inventory movement ID created for this consumption
    /// </summary>
    public int? InventoryMovementId { get; set; }
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// Production order that consumed the material
    /// </summary>
    public virtual ProductionOrder ProductionOrder { get; set; } = null!;
    
    /// <summary>
    /// Recipe ingredient this consumption is for
    /// </summary>
    public virtual RecipeIngredient RecipeIngredient { get; set; } = null!;
    
    /// <summary>
    /// Product that was consumed
    /// </summary>
    public virtual Product Product { get; set; } = null!;
    
    /// <summary>
    /// Batch that was consumed (if applicable)
    /// </summary>
    public virtual InventoryBatch? Batch { get; set; }
    
    /// <summary>
    /// Inventory movement created for this consumption
    /// </summary>
    public virtual InventoryMovement? InventoryMovement { get; set; }
    
    // Calculated properties
    
    /// <summary>
    /// Indicates if there was a significant variance (>5%)
    /// </summary>
    public bool HasSignificantVariance => Math.Abs(VariancePercentage) > 5;
    
    /// <summary>
    /// Indicates if more material was used than planned
    /// </summary>
    public bool IsOverConsumption => QuantityVariance > 0;
    
    /// <summary>
    /// Indicates if less material was used than planned
    /// </summary>
    public bool IsUnderConsumption => QuantityVariance < 0;
    
    /// <summary>
    /// Efficiency percentage (100% = exactly as planned, >100% = over-consumption)
    /// </summary>
    public decimal EfficiencyPercentage
    {
        get
        {
            if (PlannedQuantity == 0) return 100;
            return (ActualQuantity / PlannedQuantity) * 100;
        }
    }
}
