using Microsoft.EntityFrameworkCore;
using IFC.Application.Interfaces;
using IFC.Domain.Entities;
using IFC.Infrastructure.Data;

namespace IFC.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Inventory entity
/// </summary>
public class InventoryRepository : IInventoryRepository
{
    private readonly IFCDbContext _context;

    public InventoryRepository(IFCDbContext context)
    {
        _context = context;
    }

    public async Task<Inventory?> GetByIdAsync(int id)
    {
        return await _context.Inventories
            .Include(i => i.Product)
            .FirstOrDefaultAsync(i => i.InventoryId == id);
    }

    public async Task<Inventory?> GetByProductAndCostCenterAsync(int productId, int costCenterId)
    {
        return await _context.Inventories
            .Include(i => i.Product)
            .FirstOrDefaultAsync(i => i.ProductId == productId && i.CostCenterId == costCenterId);
    }

    public async Task<IEnumerable<Inventory>> GetByCostCenterIdAsync(int costCenterId)
    {
        return await _context.Inventories
            .Include(i => i.Product)
            .Where(i => i.CostCenterId == costCenterId)
            .ToListAsync();
    }

    public async Task<IEnumerable<Inventory>> GetByProductIdAsync(int productId)
    {
        return await _context.Inventories
            .Include(i => i.Product)
            .Where(i => i.ProductId == productId)
            .ToListAsync();
    }

    public async Task<decimal> GetAvailableQuantityAsync(int productId, int costCenterId)
    {
        var inventory = await GetByProductAndCostCenterAsync(productId, costCenterId);
        return inventory?.AvailableQuantity ?? 0;
    }

    public async Task<decimal> GetTotalQuantityAsync(int productId, int costCenterId)
    {
        var inventory = await GetByProductAndCostCenterAsync(productId, costCenterId);
        return inventory?.TotalQuantity ?? 0;
    }

    public async Task<bool> ReserveInventoryAsync(int productId, int costCenterId, decimal quantity, int productionOrderId)
    {
        var inventory = await GetByProductAndCostCenterAsync(productId, costCenterId);
        if (inventory == null || inventory.AvailableQuantity < quantity)
            return false;

        inventory.ReservedQuantity += quantity;
        inventory.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ReleaseReservedInventoryAsync(int productId, int costCenterId, decimal quantity, int productionOrderId)
    {
        var inventory = await GetByProductAndCostCenterAsync(productId, costCenterId);
        if (inventory == null || inventory.ReservedQuantity < quantity)
            return false;

        inventory.ReservedQuantity -= quantity;
        inventory.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<Inventory> UpdateInventoryAsync(int productId, int costCenterId, decimal quantityChange, decimal unitCost)
    {
        var inventory = await GetByProductAndCostCenterAsync(productId, costCenterId);
        if (inventory == null)
        {
            // Create new inventory record
            inventory = new Inventory
            {
                ProductId = productId,
                CostCenterId = costCenterId,
                TotalQuantity = quantityChange,
                AvailableQuantity = quantityChange,
                ReservedQuantity = 0,
                AverageCost = unitCost,
                LastCost = unitCost,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
            _context.Inventories.Add(inventory);
        }
        else
        {
            // Update existing inventory
            inventory.TotalQuantity += quantityChange;
            inventory.AvailableQuantity += quantityChange;
            inventory.LastCost = unitCost;

            // Calculate weighted average cost
            if (inventory.TotalQuantity > 0)
            {
                var totalValue = (inventory.TotalQuantity - quantityChange) * inventory.AverageCost + quantityChange * unitCost;
                inventory.AverageCost = totalValue / inventory.TotalQuantity;
            }

            inventory.ModifiedDate = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
        return inventory;
    }

    public async Task<InventoryMovement> RecordMovementAsync(InventoryMovement movement)
    {
        _context.InventoryMovements.Add(movement);
        await _context.SaveChangesAsync();
        return movement;
    }

    public async Task<IEnumerable<InventoryMovement>> GetMovementsAsync(int productId, int? costCenterId = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        var query = _context.InventoryMovements
            .Include(m => m.Product)
            .Where(m => m.ProductId == productId);

        if (costCenterId.HasValue)
            query = query.Where(m => m.CostCenterId == costCenterId.Value);

        if (fromDate.HasValue)
            query = query.Where(m => m.MovementDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(m => m.MovementDate <= toDate.Value);

        return await query.OrderByDescending(m => m.MovementDate).ToListAsync();
    }

    public async Task<IEnumerable<InventoryBatch>> GetBatchesAsync(int productId, int costCenterId, bool includeExpired = false)
    {
        var query = _context.InventoryBatches
            .Include(b => b.Product)
            .Where(b => b.ProductId == productId && b.CostCenterId == costCenterId);

        if (!includeExpired)
            query = query.Where(b => b.ExpirationDate > DateTime.UtcNow);

        return await query.OrderBy(b => b.ExpirationDate).ToListAsync();
    }

    public async Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(int daysFromNow, int? costCenterId = null)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(daysFromNow);

        var query = _context.InventoryBatches
            .Include(b => b.Product)
            .Where(b => b.ExpirationDate <= cutoffDate && b.Quantity > 0);

        if (costCenterId.HasValue)
            query = query.Where(b => b.CostCenterId == costCenterId.Value);

        return await query.OrderBy(b => b.ExpirationDate).ToListAsync();
    }

    public async Task<IEnumerable<Inventory>> GetLowInventoryAsync(int? costCenterId = null)
    {
        var query = _context.Inventories
            .Include(i => i.Product)
            .Where(i => i.AvailableQuantity <= i.MinimumLevel);

        if (costCenterId.HasValue)
            query = query.Where(i => i.CostCenterId == costCenterId.Value);

        return await query.ToListAsync();
    }

    public async Task<Inventory> AddAsync(Inventory inventory)
    {
        _context.Inventories.Add(inventory);
        await _context.SaveChangesAsync();
        return inventory;
    }

    public async Task<Inventory> UpdateAsync(Inventory inventory)
    {
        _context.Inventories.Update(inventory);
        await _context.SaveChangesAsync();
        return inventory;
    }
}
