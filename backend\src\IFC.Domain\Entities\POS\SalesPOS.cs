using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IFC.Domain.Entities.POS;

/// <summary>
/// Sales POS entity - maps to Sales_POS table
/// Replicates VB.NET POS sales integration
/// </summary>
[Table("Sales_POS")]
public class SalesPOS
{
    [Key]
    public int SalesPOSId { get; set; }

    [Column("CostCenterPOS_Id")]
    public int CostCenterPOSId { get; set; }

    [Column("CostCenterPOS_Name")]
    [MaxLength(200)]
    public string CostCenterPOSName { get; set; } = string.Empty;

    [Column("CostCenter_Id_To")]
    public int CostCenterIdTo { get; set; }

    [Column("CostCenter_Name_To")]
    [MaxLength(200)]
    public string CostCenterNameTo { get; set; } = string.Empty;

    [Column("Transaction_Code")]
    [MaxLength(50)]
    public string TransactionCode { get; set; } = string.Empty;

    [Column("Product_Id")]
    public int ProductId { get; set; }

    [Column("Product_Code")]
    [MaxLength(50)]
    public string ProductCode { get; set; } = string.Empty;

    [Column("Product_Name")]
    [MaxLength(200)]
    public string ProductName { get; set; } = string.Empty;

    [Column("Reciving_Q", TypeName = "decimal(18,4)")]
    public decimal ReceivingQuantity { get; set; }

    [Column("Cost_Product", TypeName = "decimal(18,4)")]
    public decimal CostProduct { get; set; }

    [Column("CostTotalLine", TypeName = "decimal(18,4)")]
    public decimal CostTotalLine { get; set; }

    [Column("Sales_Price", TypeName = "decimal(18,4)")]
    public decimal SalesPrice { get; set; }

    [Column("Patch_Ser")]
    public int? PatchSer { get; set; }

    [Column("Patch_Name")]
    [MaxLength(100)]
    public string? PatchName { get; set; }

    [Column("Transaction_Date_Create")]
    public DateTime TransactionDateCreate { get; set; }

    [Column("Check_No")]
    [MaxLength(50)]
    public string? CheckNo { get; set; }

    [Column("CompanyPOS_Id")]
    public int CompanyPOSId { get; set; }

    [Column("CompanyPOS_Name")]
    [MaxLength(200)]
    public string CompanyPOSName { get; set; } = string.Empty;

    [Column("OutLetPOS_Id")]
    public int OutLetPOSId { get; set; }

    [Column("OutLetPOS_Name")]
    [MaxLength(200)]
    public string OutLetPOSName { get; set; } = string.Empty;

    [Column("MethodOfPayment_Id")]
    public int? MethodOfPaymentId { get; set; }

    [Column("MethodOfPayment_Name")]
    [MaxLength(100)]
    public string? MethodOfPaymentName { get; set; }

    [Column("IsExpire")]
    public bool IsExpire { get; set; }

    [Column("Is_Recipy")]
    public bool IsRecipe { get; set; }

    [Column("Is_Production")]
    public bool IsProduction { get; set; }

    [Column("CreatedDate")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Column("ModifiedDate")]
    public DateTime? ModifiedDate { get; set; }
}
