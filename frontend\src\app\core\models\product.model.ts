export interface Product {
  productId: number;
  productCode: string;
  productName: string;
  brandName?: string;
  unitId: number;
  unitName: string;
  unitGroupId: number;
  unitQuantity: number;
  departmentId?: number;
  groupId?: number;
  subGroupId?: number;
  costPerUnit: number;
  averageCost: number;
  salesPrice: number;
  minimumStock: number;
  maximumStock: number;
  reorderPoint: number;
  notes?: string;
  isStock: boolean;
  isRecipe: boolean;
  isExpirable: boolean;
  isProduction: boolean;
  isSales: boolean;
  authorizationLevel: number;
  isActive: boolean;
  createdDate: Date;
  modifiedDate?: Date;
  recipe?: Recipe;
  inventoryRecords?: Inventory[];
}

export interface Recipe {
  recipeId: number;
  productId: number;
  recipeName: string;
  description?: string;
  version: string;
  outputQuantity: number;
  outputUnit: string;
  estimatedProductionTimeMinutes: number;
  laborCost: number;
  overheadCost: number;
  isActive: boolean;
  isDefault: boolean;
  createdDate: Date;
  modifiedDate?: Date;
  createdBy: number;
  modifiedBy?: number;
  product?: Product;
  ingredients: RecipeIngredient[];
  productionSteps?: ProductionStep[];
  totalMaterialCost: number;
  totalRecipeCost: number;
  costPerUnit: number;
  hasNestedRecipes: boolean;
}

export interface RecipeIngredient {
  recipeIngredientId: number;
  recipeId: number;
  ingredientProductId: number;
  requiredQuantity: number;
  requiredUnit: string;
  costPerUnit: number;
  sequenceOrder: number;
  notes?: string;
  isOptional: boolean;
  wastePercentage: number;
  isActive: boolean;
  createdDate: Date;
  modifiedDate?: Date;
  recipe?: Recipe;
  ingredientProduct: Product;
  totalCost: number;
  actualQuantityNeeded: number;
  isNestedRecipe: boolean;
  isProductionItem: boolean;
  requiresProduction: boolean;
}

export interface ProductionOrder {
  productionOrderId: number;
  orderNumber: string;
  productId: number;
  recipeId: number;
  quantityToProduce: number;
  quantityProduced: number;
  unit: string;
  status: ProductionOrderStatus;
  priority: number;
  costCenterId: number;
  costCenterName: string;
  plannedStartDate: Date;
  plannedEndDate: Date;
  actualStartDate?: Date;
  actualEndDate?: Date;
  estimatedCost: number;
  actualCost: number;
  notes?: string;
  parentProductionOrderId?: number;
  hierarchyLevel: number;
  isAutoGenerated: boolean;
  createdDate: Date;
  modifiedDate?: Date;
  createdBy: number;
  modifiedBy?: number;
  product?: Product;
  recipe?: Recipe;
  parentProductionOrder?: ProductionOrder;
  childProductionOrders: ProductionOrder[];
  productionSteps: ProductionStep[];
  materialConsumptions: MaterialConsumption[];
  completionPercentage: number;
  isComplete: boolean;
  canStart: boolean;
}

export interface ProductionStep {
  productionStepId: number;
  productionOrderId: number;
  recipeId?: number;
  stepNumber: number;
  stepName: string;
  description?: string;
  stepType: ProductionStepType;
  status: ProductionStepStatus;
  estimatedDurationMinutes: number;
  actualDurationMinutes?: number;
  plannedStartTime?: Date;
  plannedEndTime?: Date;
  actualStartTime?: Date;
  actualEndTime?: Date;
  assignedWorker?: string;
  equipment?: string;
  qualityNotes?: string;
  requiresQualityApproval: boolean;
  qualityApproved: boolean;
  qualityApprovedBy?: number;
  qualityApprovedDate?: Date;
  createdDate: Date;
  modifiedDate?: Date;
  productionOrder?: ProductionOrder;
  recipe?: Recipe;
  isComplete: boolean;
  isInProgress: boolean;
  canStart: boolean;
  durationVariance?: number;
}

export interface Inventory {
  inventoryId: number;
  productId: number;
  costCenterId: number;
  costCenterName: string;
  quantityOnHand: number;
  quantityReserved: number;
  quantityAvailable: number;
  baseUnitQuantity: number;
  unitConversionFactor: number;
  averageCost: number;
  lastCost: number;
  totalValue: number;
  lastMovementDate: Date;
  lastCountDate?: Date;
  isActive: boolean;
  createdDate: Date;
  modifiedDate?: Date;
  product?: Product;
  movements: InventoryMovement[];
  batches: InventoryBatch[];
  isBelowMinimum: boolean;
  needsReorder: boolean;
  daysOfSupply?: number;
}

export interface InventoryMovement {
  movementId: number;
  inventoryId: number;
  productId: number;
  costCenterId: number;
  movementType: InventoryMovementType;
  referenceNumber: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  quantityBefore: number;
  quantityAfter: number;
  productionOrderId?: number;
  batchId?: number;
  movementDate: Date;
  userId: number;
  notes?: string;
  isPosted: boolean;
  createdDate: Date;
  inventory?: Inventory;
  product?: Product;
  productionOrder?: ProductionOrder;
  batch?: InventoryBatch;
  isInbound: boolean;
  isOutbound: boolean;
  absoluteQuantity: number;
}

export interface InventoryBatch {
  batchId: number;
  inventoryId: number;
  productId: number;
  costCenterId: number;
  batchNumber: string;
  quantity: number;
  originalQuantity: number;
  quantityReserved: number;
  quantityAvailable: number;
  unit: string;
  unitCost: number;
  totalValue: number;
  manufactureDate?: Date;
  expirationDate?: Date;
  receivedDate: Date;
  supplier?: string;
  purchaseOrderNumber?: string;
  productionOrderId?: number;
  qualityStatus: string;
  isOnHold: boolean;
  isExpired: boolean;
  isNearExpiration: boolean;
  daysUntilExpiration?: number;
  isActive: boolean;
  isUsed: boolean;
  notes?: string;
  createdDate: Date;
  modifiedDate?: Date;
  inventory?: Inventory;
  product?: Product;
  productionOrder?: ProductionOrder;
  movements: InventoryMovement[];
  materialConsumptions: MaterialConsumption[];
  canBeUsed: boolean;
  shelfLifePercentage?: number;
}

export interface MaterialConsumption {
  consumptionId: number;
  productionOrderId: number;
  recipeIngredientId: number;
  productId: number;
  costCenterId: number;
  batchId?: number;
  plannedQuantity: number;
  actualQuantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  quantityVariance: number;
  variancePercentage: number;
  consumptionDate: Date;
  userId: number;
  varianceReason?: string;
  notes?: string;
  isPosted: boolean;
  inventoryMovementId?: number;
  createdDate: Date;
  modifiedDate?: Date;
  productionOrder?: ProductionOrder;
  recipeIngredient?: RecipeIngredient;
  product?: Product;
  batch?: InventoryBatch;
  inventoryMovement?: InventoryMovement;
  hasSignificantVariance: boolean;
  isOverConsumption: boolean;
  isUnderConsumption: boolean;
  efficiencyPercentage: number;
}

// Enums
export enum ProductionOrderStatus {
  Planned = 1,
  MaterialReservation = 2,
  WaitingForDependencies = 3,
  ReadyToStart = 4,
  InProgress = 5,
  Paused = 6,
  Completed = 7,
  Cancelled = 8,
  Failed = 9,
  OnHold = 10
}

export enum ProductionStepType {
  MaterialPreparation = 1,
  Mixing = 2,
  Cooking = 3,
  Cooling = 4,
  Assembly = 5,
  Packaging = 6,
  QualityControl = 7,
  Waiting = 8,
  SubRecipeProduction = 9,
  MaterialConsumption = 10,
  FinalInspection = 11,
  Other = 99
}

export enum ProductionStepStatus {
  Pending = 1,
  Ready = 2,
  InProgress = 3,
  Paused = 4,
  Completed = 5,
  Skipped = 6,
  Failed = 7,
  OnHold = 8
}

export enum InventoryMovementType {
  Opening = 1,
  Purchase = 2,
  ProductionReceipt = 3,
  TransferIn = 4,
  AdjustmentIn = 5,
  CustomerReturn = 6,
  Sales = 101,
  ProductionConsumption = 102,
  TransferOut = 103,
  AdjustmentOut = 104,
  Waste = 105,
  SupplierReturn = 106,
  PhysicalCount = 201,
  CycleCount = 202,
  Reservation = 301,
  ReservationRelease = 302,
  Expiration = 401,
  QualityHold = 402,
  QualityRelease = 403
}
