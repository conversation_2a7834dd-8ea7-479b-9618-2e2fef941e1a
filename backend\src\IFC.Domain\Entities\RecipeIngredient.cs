using System.ComponentModel.DataAnnotations;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents an ingredient in a recipe with its required quantity
/// </summary>
public class RecipeIngredient
{
    [Key]
    public int RecipeIngredientId { get; set; }
    
    /// <summary>
    /// The recipe this ingredient belongs to
    /// </summary>
    public int RecipeId { get; set; }
    
    /// <summary>
    /// The product used as an ingredient
    /// </summary>
    public int IngredientProductId { get; set; }
    
    /// <summary>
    /// Quantity of this ingredient required
    /// </summary>
    public decimal RequiredQuantity { get; set; }
    
    /// <summary>
    /// Unit of measure for the required quantity
    /// </summary>
    [StringLength(50)]
    public string RequiredUnit { get; set; } = string.Empty;
    
    /// <summary>
    /// Cost per unit of this ingredient at the time of recipe creation
    /// </summary>
    public decimal CostPerUnit { get; set; }
    
    /// <summary>
    /// Sequence order for this ingredient in the recipe
    /// </summary>
    public int SequenceOrder { get; set; }
    
    /// <summary>
    /// Optional notes for this ingredient (e.g., preparation instructions)
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Indicates if this ingredient is optional
    /// </summary>
    public bool IsOptional { get; set; } = false;
    
    /// <summary>
    /// Waste percentage expected for this ingredient (0-100)
    /// </summary>
    public decimal WastePercentage { get; set; } = 0;
    
    /// <summary>
    /// Indicates if this ingredient is active in the recipe
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// The recipe this ingredient belongs to
    /// </summary>
    public virtual Recipe Recipe { get; set; } = null!;
    
    /// <summary>
    /// The product used as an ingredient
    /// </summary>
    public virtual Product IngredientProduct { get; set; } = null!;
    
    // Calculated properties
    
    /// <summary>
    /// Total cost for this ingredient including waste
    /// </summary>
    public decimal TotalCost
    {
        get
        {
            var adjustedQuantity = RequiredQuantity * (1 + WastePercentage / 100);
            return adjustedQuantity * CostPerUnit;
        }
    }
    
    /// <summary>
    /// Actual quantity needed including waste
    /// </summary>
    public decimal ActualQuantityNeeded
    {
        get
        {
            return RequiredQuantity * (1 + WastePercentage / 100);
        }
    }
    
    /// <summary>
    /// Checks if this ingredient is a recipe itself (nested recipe)
    /// </summary>
    public bool IsNestedRecipe => IngredientProduct?.IsRecipe == true;
    
    /// <summary>
    /// Checks if this ingredient is a production item
    /// </summary>
    public bool IsProductionItem => IngredientProduct?.IsProduction == true;
    
    /// <summary>
    /// Checks if this ingredient requires production (either recipe or production item)
    /// </summary>
    public bool RequiresProduction => IsNestedRecipe || IsProductionItem;
}
