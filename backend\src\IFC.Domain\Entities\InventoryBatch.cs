using System.ComponentModel.DataAnnotations;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents a batch or lot of inventory for expirable products
/// </summary>
public class InventoryBatch
{
    [Key]
    public int BatchId { get; set; }
    
    /// <summary>
    /// Inventory record this batch belongs to
    /// </summary>
    public int InventoryId { get; set; }
    
    /// <summary>
    /// Product this batch is for
    /// </summary>
    public int ProductId { get; set; }
    
    /// <summary>
    /// Cost center where this batch is located
    /// </summary>
    public int CostCenterId { get; set; }
    
    /// <summary>
    /// Batch or lot number
    /// </summary>
    [Required]
    [StringLength(50)]
    public string BatchNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// Current quantity in this batch
    /// </summary>
    public decimal Quantity { get; set; }
    
    /// <summary>
    /// Original quantity when batch was created
    /// </summary>
    public decimal OriginalQuantity { get; set; }
    
    /// <summary>
    /// Quantity reserved for production orders
    /// </summary>
    public decimal QuantityReserved { get; set; } = 0;
    
    /// <summary>
    /// Available quantity (Quantity - Reserved)
    /// </summary>
    public decimal QuantityAvailable => Quantity - QuantityReserved;
    
    /// <summary>
    /// Unit of measure for quantities
    /// </summary>
    [StringLength(50)]
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Cost per unit for this batch
    /// </summary>
    public decimal UnitCost { get; set; }
    
    /// <summary>
    /// Total value of this batch
    /// </summary>
    public decimal TotalValue => Quantity * UnitCost;
    
    /// <summary>
    /// Manufacturing or production date
    /// </summary>
    public DateTime? ManufactureDate { get; set; }
    
    /// <summary>
    /// Expiration date
    /// </summary>
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// Received date (when batch was received into inventory)
    /// </summary>
    public DateTime ReceivedDate { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Supplier or source of this batch
    /// </summary>
    [StringLength(200)]
    public string? Supplier { get; set; }
    
    /// <summary>
    /// Purchase order number or reference
    /// </summary>
    [StringLength(50)]
    public string? PurchaseOrderNumber { get; set; }
    
    /// <summary>
    /// Production order that created this batch (for produced items)
    /// </summary>
    public int? ProductionOrderId { get; set; }
    
    /// <summary>
    /// Quality status of this batch
    /// </summary>
    [StringLength(50)]
    public string QualityStatus { get; set; } = "Approved";
    
    /// <summary>
    /// Indicates if this batch is on quality hold
    /// </summary>
    public bool IsOnHold { get; set; } = false;
    
    /// <summary>
    /// Indicates if this batch is expired
    /// </summary>
    public bool IsExpired => ExpirationDate.HasValue && ExpirationDate.Value < DateTime.UtcNow;
    
    /// <summary>
    /// Indicates if this batch is near expiration (within warning period)
    /// </summary>
    public bool IsNearExpiration
    {
        get
        {
            if (!ExpirationDate.HasValue) return false;
            var warningDays = 7; // Could be configurable
            return ExpirationDate.Value <= DateTime.UtcNow.AddDays(warningDays);
        }
    }
    
    /// <summary>
    /// Days until expiration (negative if expired)
    /// </summary>
    public int? DaysUntilExpiration
    {
        get
        {
            if (!ExpirationDate.HasValue) return null;
            return (int)(ExpirationDate.Value - DateTime.UtcNow).TotalDays;
        }
    }
    
    /// <summary>
    /// Indicates if this batch is active and usable
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Indicates if this batch has been used (quantity consumed)
    /// </summary>
    public bool IsUsed { get; set; } = false;
    
    /// <summary>
    /// Notes about this batch
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// Inventory record this batch belongs to
    /// </summary>
    public virtual Inventory Inventory { get; set; } = null!;
    
    /// <summary>
    /// Product this batch is for
    /// </summary>
    public virtual Product Product { get; set; } = null!;
    
    /// <summary>
    /// Production order that created this batch (if applicable)
    /// </summary>
    public virtual ProductionOrder? ProductionOrder { get; set; }
    
    /// <summary>
    /// Inventory movements for this batch
    /// </summary>
    public virtual ICollection<InventoryMovement> Movements { get; set; } = new List<InventoryMovement>();
    
    /// <summary>
    /// Material consumptions from this batch
    /// </summary>
    public virtual ICollection<MaterialConsumption> MaterialConsumptions { get; set; } = new List<MaterialConsumption>();
    
    // Calculated properties
    
    /// <summary>
    /// Indicates if this batch can be used for production
    /// </summary>
    public bool CanBeUsed => IsActive && !IsOnHold && !IsExpired && Quantity > 0;
    
    /// <summary>
    /// Shelf life remaining as percentage (0-100)
    /// </summary>
    public decimal? ShelfLifePercentage
    {
        get
        {
            if (!ManufactureDate.HasValue || !ExpirationDate.HasValue) return null;
            
            var totalShelfLife = (ExpirationDate.Value - ManufactureDate.Value).TotalDays;
            var remainingShelfLife = (ExpirationDate.Value - DateTime.UtcNow).TotalDays;
            
            if (totalShelfLife <= 0) return 0;
            
            var percentage = (decimal)(remainingShelfLife / totalShelfLife * 100);
            return Math.Max(0, Math.Min(100, percentage));
        }
    }
}
