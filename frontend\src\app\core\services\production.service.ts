import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, firstValueFrom } from 'rxjs';
import { environment } from '../../../environments/environment';
import { 
  ProductionOrder, 
  ProductionOrderStatus 
} from '../models/product.model';
import { 
  ProductionValidationResult,
  ProductionHierarchy,
  MaterialRequirements,
  MaterialReservationResult
} from '../models/production.model';

@Injectable({
  providedIn: 'root'
})
export class ProductionService {
  private http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/production`;

  /**
   * Creates a new production order with recursive processing
   * Replicates VB.NET: GetProductionRecipyData() + RowEffectQuantity() but with recursion
   */
  async createProductionOrder(
    productId: number,
    quantity: number,
    costCenterId: number,
    plannedStartDate: Date,
    priority: number = 5
  ): Promise<ProductionOrder> {
    const request = {
      productId,
      quantity,
      costCenterId,
      plannedStartDate: plannedStartDate.toISOString(),
      priority
    };

    return firstValueFrom(
      this.http.post<ProductionOrder>(`${this.apiUrl}/orders`, request)
    );
  }

  /**
   * Validates a production order before creation
   * Replicates VB.NET: RowEffectQuantity() validation but with recursive checking
   */
  async validateProductionOrder(
    productId: number,
    quantity: number,
    costCenterId: number
  ): Promise<ProductionValidationResult> {
    const request = {
      productId,
      quantity,
      costCenterId
    };

    return firstValueFrom(
      this.http.post<ProductionValidationResult>(`${this.apiUrl}/orders/validate`, request)
    );
  }

  /**
   * Gets production hierarchy for a product
   * Shows the complete nested recipe structure like VB.NET DGVRecipe grid
   */
  async getProductionHierarchy(productId: number, quantity: number = 1): Promise<ProductionHierarchy> {
    const params = new HttpParams().set('quantity', quantity.toString());
    
    return firstValueFrom(
      this.http.get<ProductionHierarchy>(`${this.apiUrl}/hierarchy/${productId}`, { params })
    );
  }

  /**
   * Calculates material requirements recursively
   * Replicates VB.NET recipe ingredient calculation but with full recursion
   */
  async getMaterialRequirements(productId: number, quantity: number = 1): Promise<MaterialRequirements> {
    const params = new HttpParams().set('quantity', quantity.toString());
    
    return firstValueFrom(
      this.http.get<MaterialRequirements>(`${this.apiUrl}/materials/${productId}`, { params })
    );
  }

  /**
   * Gets a production order with full hierarchy
   */
  async getProductionOrderById(id: number): Promise<ProductionOrder | null> {
    try {
      return await firstValueFrom(
        this.http.get<ProductionOrder>(`${this.apiUrl}/orders/${id}`)
      );
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Gets production orders by status
   * Replicates VB.NET production order filtering
   */
  async getProductionOrdersByStatus(
    status?: ProductionOrderStatus,
    costCenterId?: number
  ): Promise<ProductionOrder[]> {
    let params = new HttpParams();
    
    if (status !== undefined) {
      params = params.set('status', status.toString());
    }
    
    if (costCenterId !== undefined) {
      params = params.set('costCenterId', costCenterId.toString());
    }

    return firstValueFrom(
      this.http.get<ProductionOrder[]>(`${this.apiUrl}/orders`, { params })
    );
  }

  /**
   * Starts production for an order
   * Replicates VB.NET production start process
   */
  async startProduction(id: number, userId: number): Promise<ProductionOrder> {
    const request = { userId };
    
    return firstValueFrom(
      this.http.post<ProductionOrder>(`${this.apiUrl}/orders/${id}/start`, request)
    );
  }

  /**
   * Completes production for an order
   */
  async completeProduction(
    id: number, 
    actualQuantityProduced: number, 
    userId: number
  ): Promise<ProductionOrder> {
    const request = {
      actualQuantityProduced,
      userId
    };
    
    return firstValueFrom(
      this.http.post<ProductionOrder>(`${this.apiUrl}/orders/${id}/complete`, request)
    );
  }

  /**
   * Cancels a production order
   */
  async cancelProduction(id: number, reason: string, userId: number): Promise<ProductionOrder> {
    const request = {
      reason,
      userId
    };
    
    return firstValueFrom(
      this.http.post<ProductionOrder>(`${this.apiUrl}/orders/${id}/cancel`, request)
    );
  }

  /**
   * Reserves materials for production
   * Replicates VB.NET inventory reservation logic
   */
  async reserveMaterials(id: number): Promise<MaterialReservationResult> {
    return firstValueFrom(
      this.http.post<MaterialReservationResult>(`${this.apiUrl}/orders/${id}/reserve-materials`, {})
    );
  }

  /**
   * Releases reserved materials
   */
  async releaseMaterials(id: number): Promise<MaterialReservationResult> {
    return firstValueFrom(
      this.http.post<MaterialReservationResult>(`${this.apiUrl}/orders/${id}/release-materials`, {})
    );
  }

  // Observable versions for reactive programming
  
  getProductionOrdersObservable(
    status?: ProductionOrderStatus,
    costCenterId?: number
  ): Observable<ProductionOrder[]> {
    let params = new HttpParams();
    
    if (status !== undefined) {
      params = params.set('status', status.toString());
    }
    
    if (costCenterId !== undefined) {
      params = params.set('costCenterId', costCenterId.toString());
    }

    return this.http.get<ProductionOrder[]>(`${this.apiUrl}/orders`, { params });
  }

  getProductionHierarchyObservable(productId: number, quantity: number = 1): Observable<ProductionHierarchy> {
    const params = new HttpParams().set('quantity', quantity.toString());
    
    return this.http.get<ProductionHierarchy>(`${this.apiUrl}/hierarchy/${productId}`, { params });
  }

  validateProductionOrderObservable(
    productId: number,
    quantity: number,
    costCenterId: number
  ): Observable<ProductionValidationResult> {
    const request = {
      productId,
      quantity,
      costCenterId
    };

    return this.http.post<ProductionValidationResult>(`${this.apiUrl}/orders/validate`, request);
  }
}
