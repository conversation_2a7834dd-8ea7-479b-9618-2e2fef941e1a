using System.ComponentModel.DataAnnotations;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents inventory levels and movements for products
/// </summary>
public class Inventory
{
    [Key]
    public int InventoryId { get; set; }
    
    /// <summary>
    /// Product this inventory record is for
    /// </summary>
    public int ProductId { get; set; }
    
    /// <summary>
    /// Cost center where the inventory is located
    /// </summary>
    public int CostCenterId { get; set; }
    
    [StringLength(200)]
    public string CostCenterName { get; set; } = string.Empty;
    
    /// <summary>
    /// Current quantity on hand
    /// </summary>
    public decimal QuantityOnHand { get; set; }
    
    /// <summary>
    /// Quantity reserved for production orders
    /// </summary>
    public decimal QuantityReserved { get; set; } = 0;
    
    /// <summary>
    /// Quantity available for use (OnHand - Reserved)
    /// </summary>
    public decimal QuantityAvailable => QuantityOnHand - QuantityReserved;
    
    /// <summary>
    /// Base unit quantity for unit conversions
    /// </summary>
    public decimal BaseUnitQuantity { get; set; }
    
    /// <summary>
    /// Unit conversion factor
    /// </summary>
    public decimal UnitConversionFactor { get; set; } = 1;
    
    /// <summary>
    /// Average cost per unit
    /// </summary>
    public decimal AverageCost { get; set; }
    
    /// <summary>
    /// Last cost per unit
    /// </summary>
    public decimal LastCost { get; set; }
    
    /// <summary>
    /// Total value of inventory (Quantity * Average Cost)
    /// </summary>
    public decimal TotalValue => QuantityOnHand * AverageCost;
    
    /// <summary>
    /// Last movement date
    /// </summary>
    public DateTime LastMovementDate { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Last physical count date
    /// </summary>
    public DateTime? LastCountDate { get; set; }
    
    /// <summary>
    /// Indicates if this inventory location is active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// Product this inventory record is for
    /// </summary>
    public virtual Product Product { get; set; } = null!;
    
    /// <summary>
    /// Inventory movements for this product/location
    /// </summary>
    public virtual ICollection<InventoryMovement> Movements { get; set; } = new List<InventoryMovement>();
    
    /// <summary>
    /// Batch/lot records if product is expirable
    /// </summary>
    public virtual ICollection<InventoryBatch> Batches { get; set; } = new List<InventoryBatch>();
    
    // Calculated properties
    
    /// <summary>
    /// Indicates if inventory is below minimum stock level
    /// </summary>
    public bool IsBelowMinimum => QuantityOnHand < Product.MinimumStock;
    
    /// <summary>
    /// Indicates if inventory is at or below reorder point
    /// </summary>
    public bool NeedsReorder => QuantityOnHand <= Product.ReorderPoint;
    
    /// <summary>
    /// Days of supply remaining based on average consumption
    /// </summary>
    public decimal? DaysOfSupply
    {
        get
        {
            // This would need to be calculated based on historical consumption data
            // For now, returning null - would be implemented with consumption tracking
            return null;
        }
    }
}
