using IFC.Domain.Entities;
using IFC.Domain.Enums;

namespace IFC.Application.Interfaces;

/// <summary>
/// Repository interface for Inventory entity
/// </summary>
public interface IInventoryRepository
{
    /// <summary>
    /// Gets inventory by product and cost center
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="costCenterId">Cost center ID</param>
    /// <returns>Inventory record or null if not found</returns>
    Task<Inventory?> GetByProductAndCostCenterAsync(int productId, int costCenterId);
    
    /// <summary>
    /// Gets all inventory for a product across all cost centers
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <returns>List of inventory records</returns>
    Task<IEnumerable<Inventory>> GetByProductIdAsync(int productId);
    
    /// <summary>
    /// Gets all inventory for a cost center
    /// </summary>
    /// <param name="costCenterId">Cost center ID</param>
    /// <returns>List of inventory records</returns>
    Task<IEnumerable<Inventory>> GetByCostCenterIdAsync(int costCenterId);
    
    /// <summary>
    /// Gets available quantity for a product at a cost center
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="costCenterId">Cost center ID</param>
    /// <returns>Available quantity (OnHand - Reserved)</returns>
    Task<decimal> GetAvailableQuantityAsync(int productId, int costCenterId);
    
    /// <summary>
    /// Gets inventory movements for a product
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <returns>List of inventory movements</returns>
    Task<IEnumerable<InventoryMovement>> GetMovementsAsync(
        int productId, 
        int? costCenterId = null, 
        DateTime? fromDate = null, 
        DateTime? toDate = null);
    
    /// <summary>
    /// Gets inventory batches for expirable products
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="costCenterId">Cost center ID</param>
    /// <param name="includeExpired">Include expired batches</param>
    /// <returns>List of inventory batches</returns>
    Task<IEnumerable<InventoryBatch>> GetBatchesAsync(int productId, int costCenterId, bool includeExpired = false);
    
    /// <summary>
    /// Gets batches expiring within specified days
    /// </summary>
    /// <param name="days">Number of days to check</param>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>Batches expiring soon</returns>
    Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(int days, int? costCenterId = null);
    
    /// <summary>
    /// Reserves inventory for production
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="costCenterId">Cost center ID</param>
    /// <param name="quantity">Quantity to reserve</param>
    /// <param name="productionOrderId">Production order ID</param>
    /// <returns>True if reservation successful</returns>
    Task<bool> ReserveInventoryAsync(int productId, int costCenterId, decimal quantity, int productionOrderId);
    
    /// <summary>
    /// Releases reserved inventory
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="costCenterId">Cost center ID</param>
    /// <param name="quantity">Quantity to release</param>
    /// <param name="productionOrderId">Production order ID</param>
    /// <returns>True if release successful</returns>
    Task<bool> ReleaseReservedInventoryAsync(int productId, int costCenterId, decimal quantity, int productionOrderId);
    
    /// <summary>
    /// Records an inventory movement
    /// </summary>
    /// <param name="movement">Inventory movement to record</param>
    /// <returns>Recorded movement with ID</returns>
    Task<InventoryMovement> RecordMovementAsync(InventoryMovement movement);
    
    /// <summary>
    /// Updates inventory quantities after movement
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="costCenterId">Cost center ID</param>
    /// <param name="quantityChange">Quantity change (positive for receipts, negative for issues)</param>
    /// <param name="newUnitCost">New unit cost (for cost averaging)</param>
    /// <returns>Updated inventory record</returns>
    Task<Inventory> UpdateInventoryAsync(int productId, int costCenterId, decimal quantityChange, decimal newUnitCost);
    
    /// <summary>
    /// Gets products with low inventory
    /// </summary>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>Products below minimum stock levels</returns>
    Task<IEnumerable<Inventory>> GetLowInventoryAsync(int? costCenterId = null);
    
    /// <summary>
    /// Adds new inventory record
    /// </summary>
    /// <param name="inventory">Inventory to add</param>
    /// <returns>Added inventory with ID</returns>
    Task<Inventory> AddAsync(Inventory inventory);
    
    /// <summary>
    /// Updates existing inventory record
    /// </summary>
    /// <param name="inventory">Inventory to update</param>
    /// <returns>Updated inventory</returns>
    Task<Inventory> UpdateAsync(Inventory inventory);
}
