using Microsoft.EntityFrameworkCore;
using IFC.Domain.Entities;
using IFC.Application.Interfaces;
using IFC.Infrastructure.Data;

namespace IFC.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Product entity
/// Replicates the exact VB.NET data access patterns
/// </summary>
public class ProductRepository : IProductRepository
{
    private readonly IFCDbContext _context;

    public ProductRepository(IFCDbContext context)
    {
        _context = context;
    }

    public async Task<Product?> GetByIdAsync(int productId)
    {
        return await _context.Products
            .Include(p => p.Recipe)
                .ThenInclude(r => r!.Ingredients)
                    .ThenInclude(i => i.IngredientProduct)
            .Include(p => p.InventoryRecords)
            .FirstOrDefaultAsync(p => p.ProductId == productId);
    }

    public async Task<Product?> GetByIdWithRecipeAsync(int productId)
    {
        return await _context.Products
            .Include(p => p.Recipe)
                .ThenInclude(r => r!.Ingredients)
                    .ThenInclude(i => i.IngredientProduct)
                        .ThenInclude(ip => ip!.Recipe)
                            .ThenInclude(r => r!.Ingredients)
            .FirstOrDefaultAsync(p => p.ProductId == productId);
    }

    public async Task<Product?> GetByCodeAsync(string productCode)
    {
        return await _context.Products
            .Include(p => p.Recipe)
                .ThenInclude(r => r!.Ingredients)
                    .ThenInclude(i => i.IngredientProduct)
            .FirstOrDefaultAsync(p => p.ProductCode == productCode);
    }

    public async Task<IEnumerable<Product>> GetProductionProductsAsync()
    {
        // Replicates VB.NET query: "SELECT * FROM ProductsTbl WHERE IsProduction=1 OR IsRecipe=1"
        return await _context.Products
            .Where(p => p.IsProduction || p.IsRecipe)
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetRecipeProductsAsync()
    {
        return await _context.Products
            .Where(p => p.IsRecipe)
            .Where(p => p.IsActive)
            .Include(p => p.Recipe)
                .ThenInclude(r => r!.Ingredients)
                    .ThenInclude(i => i.IngredientProduct)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetRawMaterialsAsync()
    {
        return await _context.Products
            .Where(p => !p.IsRecipe && !p.IsProduction)
            .Where(p => p.IsStock)
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
    {
        return await _context.Products
            .Where(p => p.ProductCode.Contains(searchTerm) ||
                       p.ProductName.Contains(searchTerm))
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetProductsByDepartmentAsync(int departmentId)
    {
        return await _context.Products
            .Where(p => p.DepartmentId == departmentId)
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetProductsByGroupAsync(int groupId)
    {
        return await _context.Products
            .Where(p => p.GroupId == groupId)
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetExpirableProductsAsync()
    {
        return await _context.Products
            .Where(p => p.IsExpirable)
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<bool> HasRecipeAsync(int productId)
    {
        return await _context.Recipes
            .AnyAsync(r => r.ProductId == productId && r.IsActive);
    }

    public async Task<bool> IsUsedInRecipesAsync(int productId)
    {
        return await _context.RecipeIngredients
            .AnyAsync(ri => ri.IngredientProductId == productId && ri.IsActive);
    }

    public async Task<IEnumerable<Product>> GetProductsUsingIngredientAsync(int ingredientProductId)
    {
        var productIds = await _context.RecipeIngredients
            .Where(ri => ri.IngredientProductId == ingredientProductId && ri.IsActive)
            .Select(ri => ri.Recipe.ProductId)
            .Distinct()
            .ToListAsync();

        return await _context.Products
            .Where(p => productIds.Contains(p.ProductId))
            .Include(p => p.Recipe)
            .ToListAsync();
    }

    public async Task<decimal> GetAverageCostAsync(int productId)
    {
        var product = await _context.Products
            .FirstOrDefaultAsync(p => p.ProductId == productId);

        return product?.AverageCost ?? 0;
    }

    public async Task<Product> AddAsync(Product product)
    {
        _context.Products.Add(product);
        await _context.SaveChangesAsync();
        return product;
    }

    public async Task<Product> UpdateAsync(Product product)
    {
        product.ModifiedDate = DateTime.UtcNow;
        _context.Products.Update(product);
        await _context.SaveChangesAsync();
        return product;
    }

    public async Task<bool> DeleteAsync(int productId)
    {
        var product = await _context.Products.FindAsync(productId);
        if (product == null) return false;

        // Soft delete - set IsActive to false
        product.IsActive = false;
        product.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ExistsAsync(int productId)
    {
        return await _context.Products
            .AnyAsync(p => p.ProductId == productId && p.IsActive);
    }

    public async Task<bool> CodeExistsAsync(string productCode, int? excludeProductId = null)
    {
        var query = _context.Products.Where(p => p.ProductCode == productCode && p.IsActive);

        if (excludeProductId.HasValue)
            query = query.Where(p => p.ProductId != excludeProductId.Value);

        return await query.AnyAsync();
    }

    public async Task<IEnumerable<Product>> GetAllAsync()
    {
        return await _context.Products
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetByTypeAsync(bool? isRecipe = null, bool? isProduction = null, bool? isSales = null)
    {
        var query = _context.Products.Where(p => p.IsActive);

        if (isRecipe.HasValue)
            query = query.Where(p => p.IsRecipe == isRecipe.Value);

        if (isProduction.HasValue)
            query = query.Where(p => p.IsProduction == isProduction.Value);

        if (isSales.HasValue)
            query = query.Where(p => p.IsSales == isSales.Value);

        return await query
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> SearchAsync(string searchTerm)
    {
        return await _context.Products
            .Where(p => p.ProductCode.Contains(searchTerm) ||
                       p.ProductName.Contains(searchTerm))
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetLowInventoryProductsAsync(int? costCenterId = null)
    {
        var baseQuery = _context.Products
            .Where(p => p.IsStock && p.IsActive)
            .Include(p => p.InventoryRecords);

        if (costCenterId.HasValue)
        {
            return await baseQuery
                .Where(p => p.InventoryRecords.Any(i => i.CostCenterId == costCenterId.Value && i.QuantityOnHand <= p.ReorderPoint))
                .OrderBy(p => p.ProductCode)
                .ToListAsync();
        }

        return await baseQuery
            .Where(p => p.InventoryRecords.Any(i => i.QuantityOnHand <= p.ReorderPoint))
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<bool> ExistsAsync(string productCode, int? excludeId = null)
    {
        var query = _context.Products.Where(p => p.ProductCode == productCode && p.IsActive);

        if (excludeId.HasValue)
            query = query.Where(p => p.ProductId != excludeId.Value);

        return await query.AnyAsync();
    }

    public async Task<IEnumerable<Product>> GetPagedAsync(int page, int pageSize)
    {
        return await _context.Products
            .Where(p => p.IsActive)
            .OrderBy(p => p.ProductCode)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<int> GetCountAsync()
    {
        return await _context.Products
            .CountAsync(p => p.IsActive);
    }

    public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
    {
        return await _context.Products
            .Where(p => p.IsStock && p.IsActive)
            .Include(p => p.InventoryRecords)
            .Where(p => p.InventoryRecords.Any(i => i.QuantityOnHand <= p.ReorderPoint))
            .OrderBy(p => p.ProductCode)
            .ToListAsync();
    }

    public async Task<ProductStatistics> GetProductStatisticsAsync()
    {
        var totalProducts = await _context.Products.CountAsync(p => p.IsActive);
        var recipeProducts = await _context.Products.CountAsync(p => p.IsRecipe && p.IsActive);
        var productionProducts = await _context.Products.CountAsync(p => p.IsProduction && p.IsActive);
        var stockProducts = await _context.Products.CountAsync(p => p.IsStock && p.IsActive);
        var expirableProducts = await _context.Products.CountAsync(p => p.IsExpirable && p.IsActive);

        return new ProductStatistics
        {
            TotalProducts = totalProducts,
            RecipeProducts = recipeProducts,
            ProductionProducts = productionProducts,
            StockProducts = stockProducts,
            ExpirableProducts = expirableProducts,
            RawMaterialProducts = totalProducts - recipeProducts - productionProducts
        };
    }
}

/// <summary>
/// Product statistics model
/// </summary>
public class ProductStatistics
{
    public int TotalProducts { get; set; }
    public int RecipeProducts { get; set; }
    public int ProductionProducts { get; set; }
    public int StockProducts { get; set; }
    public int ExpirableProducts { get; set; }
    public int RawMaterialProducts { get; set; }
}
