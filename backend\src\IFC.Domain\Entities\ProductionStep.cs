using System.ComponentModel.DataAnnotations;
using IFC.Domain.Enums;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents a step in the production process
/// </summary>
public class ProductionStep
{
    [Key]
    public int ProductionStepId { get; set; }
    
    /// <summary>
    /// Production order this step belongs to
    /// </summary>
    public int ProductionOrderId { get; set; }
    
    /// <summary>
    /// Recipe this step is part of (optional, for recipe-based steps)
    /// </summary>
    public int? RecipeId { get; set; }
    
    /// <summary>
    /// Step sequence number
    /// </summary>
    public int StepNumber { get; set; }
    
    [Required]
    [StringLength(200)]
    public string StepName { get; set; } = string.Empty;
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    /// <summary>
    /// Type of production step
    /// </summary>
    public ProductionStepType StepType { get; set; }
    
    /// <summary>
    /// Current status of this step
    /// </summary>
    public ProductionStepStatus Status { get; set; } = ProductionStepStatus.Pending;
    
    /// <summary>
    /// Estimated duration in minutes
    /// </summary>
    public int EstimatedDurationMinutes { get; set; }
    
    /// <summary>
    /// Actual duration in minutes
    /// </summary>
    public int? ActualDurationMinutes { get; set; }
    
    /// <summary>
    /// Planned start time for this step
    /// </summary>
    public DateTime? PlannedStartTime { get; set; }
    
    /// <summary>
    /// Planned end time for this step
    /// </summary>
    public DateTime? PlannedEndTime { get; set; }
    
    /// <summary>
    /// Actual start time
    /// </summary>
    public DateTime? ActualStartTime { get; set; }
    
    /// <summary>
    /// Actual end time
    /// </summary>
    public DateTime? ActualEndTime { get; set; }
    
    /// <summary>
    /// Worker or operator assigned to this step
    /// </summary>
    [StringLength(100)]
    public string? AssignedWorker { get; set; }
    
    /// <summary>
    /// Equipment or workstation used for this step
    /// </summary>
    [StringLength(100)]
    public string? Equipment { get; set; }
    
    /// <summary>
    /// Quality control notes
    /// </summary>
    [StringLength(500)]
    public string? QualityNotes { get; set; }
    
    /// <summary>
    /// Indicates if this step requires quality approval
    /// </summary>
    public bool RequiresQualityApproval { get; set; } = false;
    
    /// <summary>
    /// Indicates if quality approval has been given
    /// </summary>
    public bool QualityApproved { get; set; } = false;
    
    /// <summary>
    /// User who approved quality (if applicable)
    /// </summary>
    public int? QualityApprovedBy { get; set; }
    
    /// <summary>
    /// Date when quality was approved
    /// </summary>
    public DateTime? QualityApprovedDate { get; set; }
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// Production order this step belongs to
    /// </summary>
    public virtual ProductionOrder ProductionOrder { get; set; } = null!;
    
    /// <summary>
    /// Recipe this step is part of (if applicable)
    /// </summary>
    public virtual Recipe? Recipe { get; set; }
    
    // Calculated properties
    
    /// <summary>
    /// Indicates if this step is complete
    /// </summary>
    public bool IsComplete => Status == ProductionStepStatus.Completed;
    
    /// <summary>
    /// Indicates if this step is in progress
    /// </summary>
    public bool IsInProgress => Status == ProductionStepStatus.InProgress;
    
    /// <summary>
    /// Indicates if this step can be started
    /// </summary>
    public bool CanStart => Status == ProductionStepStatus.Pending;
    
    /// <summary>
    /// Duration variance in minutes (negative = faster than planned, positive = slower)
    /// </summary>
    public int? DurationVariance
    {
        get
        {
            if (ActualDurationMinutes.HasValue)
                return ActualDurationMinutes.Value - EstimatedDurationMinutes;
            return null;
        }
    }
}
