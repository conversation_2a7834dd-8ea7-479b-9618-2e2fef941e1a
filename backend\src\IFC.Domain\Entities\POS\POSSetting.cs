using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IFC.Domain.Entities.POS;

/// <summary>
/// POS Settings entity - maps to POSSetting table in POS database
/// Replicates VB.NET: "Select * from POSSetting Order By Company_NamePOS"
/// </summary>
[Table("POSSetting")]
public class POSSetting
{
    [Key]
    public int POSSettingId { get; set; }
    
    [Column("Company_IdPOS")]
    public int CompanyIdPOS { get; set; }
    
    [Column("Company_NamePOS")]
    [MaxLength(200)]
    public string CompanyNamePOS { get; set; } = string.Empty;
    
    [Column("Brand_IdPOS")]
    public int BrandIdPOS { get; set; }
    
    [Column("Brand_NamePOS")]
    [MaxLength(200)]
    public string BrandNamePOS { get; set; } = string.Empty;
    
    [Column("CostCenter_IdPOS")]
    public int CostCenterIdPOS { get; set; }
    
    [Column("CostCenter_NamePOS")]
    [MaxLength(200)]
    public string CostCenterNamePOS { get; set; } = string.Empty;
    
    [Column("IsActive")]
    public bool IsActive { get; set; } = true;
    
    [Column("CreatedDate")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("ModifiedDate")]
    public DateTime? ModifiedDate { get; set; }
}
