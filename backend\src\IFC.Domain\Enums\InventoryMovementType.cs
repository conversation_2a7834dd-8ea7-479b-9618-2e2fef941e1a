namespace IFC.Domain.Enums;

/// <summary>
/// Represents the type of inventory movement
/// </summary>
public enum InventoryMovementType
{
    /// <summary>
    /// Initial inventory setup
    /// </summary>
    Opening = 1,
    
    /// <summary>
    /// Purchase receipt
    /// </summary>
    Purchase = 2,
    
    /// <summary>
    /// Production receipt (finished goods)
    /// </summary>
    ProductionReceipt = 3,
    
    /// <summary>
    /// Transfer in from another location
    /// </summary>
    TransferIn = 4,
    
    /// <summary>
    /// Adjustment increase
    /// </summary>
    AdjustmentIn = 5,
    
    /// <summary>
    /// Return from customer
    /// </summary>
    CustomerReturn = 6,
    
    /// <summary>
    /// Sales issue
    /// </summary>
    Sales = 101,
    
    /// <summary>
    /// Production consumption (raw materials)
    /// </summary>
    ProductionConsumption = 102,
    
    /// <summary>
    /// Transfer out to another location
    /// </summary>
    TransferOut = 103,
    
    /// <summary>
    /// Adjustment decrease
    /// </summary>
    AdjustmentOut = 104,
    
    /// <summary>
    /// Waste or spoilage
    /// </summary>
    Waste = 105,
    
    /// <summary>
    /// Return to supplier
    /// </summary>
    SupplierReturn = 106,
    
    /// <summary>
    /// Physical count adjustment
    /// </summary>
    PhysicalCount = 201,
    
    /// <summary>
    /// Cycle count adjustment
    /// </summary>
    CycleCount = 202,
    
    /// <summary>
    /// Reservation for production order
    /// </summary>
    Reservation = 301,
    
    /// <summary>
    /// Release of reservation
    /// </summary>
    ReservationRelease = 302,
    
    /// <summary>
    /// Expiration or obsolescence
    /// </summary>
    Expiration = 401,
    
    /// <summary>
    /// Quality hold
    /// </summary>
    QualityHold = 402,
    
    /// <summary>
    /// Quality release
    /// </summary>
    QualityRelease = 403
}
