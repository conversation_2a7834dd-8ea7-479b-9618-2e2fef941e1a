import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, firstValueFrom } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class POSService {
  private http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/pos`;

  /**
   * Gets all POS settings
   * Replicates VB.NET: "Select * from POSSetting Order By Company_NamePOS"
   */
  async getPOSSettings(): Promise<POSSetting[]> {
    return firstValueFrom(
      this.http.get<POSSetting[]>(`${this.apiUrl}/settings`)
    );
  }

  /**
   * Gets POS settings by cost center
   * Replicates VB.NET: "select * from POSSetting Where CostCenter_IdPOS=" & CostCenter_IdPOS
   */
  async getPOSSettingsByCostCenter(costCenterId: number): Promise<POSSetting[]> {
    return firstValueFrom(
      this.http.get<POSSetting[]>(`${this.apiUrl}/settings/costcenter/${costCenterId}`)
    );
  }

  /**
   * Loads POS transaction data for date range
   * Replicates VB.NET: LoadPosData function
   */
  async loadPOSData(fromDate: Date, toDate: Date, costCenterId: number): Promise<POSTransaction[]> {
    const params = new HttpParams()
      .set('fromDate', fromDate.toISOString())
      .set('toDate', toDate.toISOString())
      .set('costCenterId', costCenterId.toString());

    return firstValueFrom(
      this.http.get<POSTransaction[]>(`${this.apiUrl}/transactions`, { params })
    );
  }

  /**
   * Loads POS data online (unprocessed transactions)
   * Replicates VB.NET: LoadPosDataOnLine function
   */
  async loadPOSDataOnline(fromDate: Date, toDate: Date): Promise<POSTransaction[]> {
    const params = new HttpParams()
      .set('fromDate', fromDate.toISOString())
      .set('toDate', toDate.toISOString());

    return firstValueFrom(
      this.http.get<POSTransaction[]>(`${this.apiUrl}/transactions/online`, { params })
    );
  }

  /**
   * Gets unprocessed POS transactions
   */
  async getUnprocessedTransactions(): Promise<POSTransaction[]> {
    return firstValueFrom(
      this.http.get<POSTransaction[]>(`${this.apiUrl}/transactions/unprocessed`)
    );
  }

  /**
   * Marks transaction as processed
   * Replicates VB.NET: UpdateCHDIDOnline function
   */
  async markTransactionAsProcessed(chDID: number): Promise<boolean> {
    return firstValueFrom(
      this.http.post<boolean>(`${this.apiUrl}/transactions/${chDID}/mark-processed`, {})
    );
  }

  /**
   * Ensures v_ProcIFC view exists
   * Replicates VB.NET: CheckExistV_ProcIFC function
   */
  async ensureViewExists(): Promise<boolean> {
    return firstValueFrom(
      this.http.post<boolean>(`${this.apiUrl}/ensure-view`, {})
    );
  }

  /**
   * Drops v_ProcIFC view if it exists
   * Replicates VB.NET: DeleteViewIFC function
   */
  async dropViewIfExists(): Promise<boolean> {
    return firstValueFrom(
      this.http.post<boolean>(`${this.apiUrl}/drop-view`, {})
    );
  }

  /**
   * Gets sales data by date range
   */
  async getSalesData(fromDate: Date, toDate: Date, costCenterId?: number): Promise<SalesPOS[]> {
    let params = new HttpParams()
      .set('fromDate', fromDate.toISOString())
      .set('toDate', toDate.toISOString());

    if (costCenterId !== undefined) {
      params = params.set('costCenterId', costCenterId.toString());
    }

    return firstValueFrom(
      this.http.get<SalesPOS[]>(`${this.apiUrl}/sales`, { params })
    );
  }

  /**
   * Tests POS database connection
   * Replicates VB.NET: TestConnectionPOS function
   */
  async testConnection(): Promise<{ connected: boolean; error?: string }> {
    return firstValueFrom(
      this.http.get<{ connected: boolean; error?: string }>(`${this.apiUrl}/test-connection`)
    );
  }

  /**
   * Executes custom POS SQL command
   * Replicates VB.NET: EXECUT_TxtPOS function
   */
  async executeCommand(sqlCommand: string): Promise<boolean> {
    return firstValueFrom(
      this.http.post<boolean>(`${this.apiUrl}/execute-command`, sqlCommand)
    );
  }

  // Observable versions for reactive programming

  getPOSSettingsObservable(): Observable<POSSetting[]> {
    return this.http.get<POSSetting[]>(`${this.apiUrl}/settings`);
  }

  loadPOSDataObservable(fromDate: Date, toDate: Date, costCenterId: number): Observable<POSTransaction[]> {
    const params = new HttpParams()
      .set('fromDate', fromDate.toISOString())
      .set('toDate', toDate.toISOString())
      .set('costCenterId', costCenterId.toString());

    return this.http.get<POSTransaction[]>(`${this.apiUrl}/transactions`, { params });
  }

  getUnprocessedTransactionsObservable(): Observable<POSTransaction[]> {
    return this.http.get<POSTransaction[]>(`${this.apiUrl}/transactions/unprocessed`);
  }
}

// POS Models (TypeScript interfaces)

export interface POSSetting {
  posSettingId: number;
  companyIdPOS: number;
  companyNamePOS: string;
  brandIdPOS: number;
  brandNamePOS: string;
  costCenterIdPOS: number;
  costCenterNamePOS: string;
  isActive: boolean;
  createdDate: Date;
  modifiedDate?: Date;
}

export interface POSTransaction {
  id: number;
  mandant: number;
  outlet: number;
  center: number;
  centerName: string;
  plu: number;
  article: string;
  quantity: number;
  price: number;
  amount: number;
  discount: number;
  taxAmount: number;
  statistDate: Date;
  isIFCDone: number;
  chDID?: number;
  payForm?: number;
  payFormName?: string;
  billNum?: string;
}

export interface SalesPOS {
  salesPOSId: number;
  costCenterPOSId: number;
  costCenterPOSName: string;
  costCenterIdTo: number;
  costCenterNameTo: string;
  transactionCode: string;
  productId: number;
  productCode: string;
  productName: string;
  receivingQuantity: number;
  costProduct: number;
  costTotalLine: number;
  salesPrice: number;
  patchSer?: number;
  patchName?: string;
  transactionDateCreate: Date;
  checkNo?: string;
  companyPOSId: number;
  companyPOSName: string;
  outLetPOSId: number;
  outLetPOSName: string;
  methodOfPaymentId?: number;
  methodOfPaymentName?: string;
  isExpire: boolean;
  isRecipe: boolean;
  isProduction: boolean;
  createdDate: Date;
  modifiedDate?: Date;
}
