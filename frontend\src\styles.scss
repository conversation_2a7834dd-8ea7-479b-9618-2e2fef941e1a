/* Global Styles for IFC Production Management System */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Custom theme colors */
:root {
  --primary-color: #1976d2;
  --accent-color: #9c27b0;
  --warn-color: #f44336;
  --success-color: #4caf50;
  --background-color: #f5f5f5;
  --surface-color: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
}

/* Global reset and base styles */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 16px 0;
  font-weight: 500;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

p {
  margin: 0 0 16px 0;
  line-height: 1.5;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }

.full-width { width: 100%; }
.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

/* Custom Material Design overrides */
.mat-mdc-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.mat-mdc-button {
  border-radius: 6px;
}

.mat-mdc-raised-button {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.mat-mdc-form-field {
  width: 100%;
}

/* Custom snackbar styles */
.error-snackbar {
  background-color: var(--warn-color) !important;
  color: white !important;
}

.success-snackbar {
  background-color: var(--success-color) !important;
  color: white !important;
}

.warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}

/* Loading spinner overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* Custom table styles */
.mat-mdc-table {
  background-color: var(--surface-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mat-mdc-header-row {
  background-color: #f5f5f5;
}

.mat-mdc-row:hover {
  background-color: #f9f9f9;
}

/* Custom chip styles */
.status-chip {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 12px;
}

/* Tree structure styles */
.tree-node {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.tree-node:hover {
  background-color: #f5f5f5;
}

.tree-node.recipe-item {
  background-color: #f3e5f5;
}

.tree-node.raw-material {
  background-color: #e8f5e8;
}

.tree-indent {
  margin-left: 24px;
}

/* Production hierarchy styles */
.hierarchy-level-0 { border-left: 4px solid #1976d2; }
.hierarchy-level-1 { border-left: 4px solid #9c27b0; }
.hierarchy-level-2 { border-left: 4px solid #ff9800; }
.hierarchy-level-3 { border-left: 4px solid #4caf50; }
.hierarchy-level-4 { border-left: 4px solid #f44336; }

/* Validation message styles */
.validation-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 4px 0;
}

.validation-message.error {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #f44336;
}

.validation-message.warning {
  background-color: #fff3e0;
  color: #ef6c00;
  border-left: 4px solid #ff9800;
}

.validation-message.success {
  background-color: #e8f5e8;
  color: #2e7d32;
  border-left: 4px solid #4caf50;
}

.validation-message.info {
  background-color: #e3f2fd;
  color: #1976d2;
  border-left: 4px solid #2196f3;
}

/* Recipe editor specific styles */
.recipe-tree {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: var(--surface-color);
}

.ingredient-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.ingredient-item:last-child {
  border-bottom: none;
}

.ingredient-icon {
  margin-right: 12px;
  color: var(--primary-color);
}

.ingredient-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ingredient-name {
  font-weight: 500;
  color: var(--text-primary);
}

.ingredient-quantity {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.ingredient-cost {
  font-weight: 500;
  color: var(--success-color);
}

/* Production order form styles */
.production-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: var(--surface-color);
}

.form-section-title {
  margin: 0 0 16px 0;
  color: var(--primary-color);
  font-weight: 500;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row .mat-mdc-form-field {
  flex: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .tree-node {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .ingredient-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .mat-mdc-card {
    margin: 8px;
  }
  
  h1 { font-size: 1.5rem; }
  h2 { font-size: 1.25rem; }
  h3 { font-size: 1.125rem; }
}

/* Print styles */
@media print {
  .mat-mdc-button,
  .mat-mdc-icon-button,
  .action-buttons {
    display: none !important;
  }
  
  .mat-mdc-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
