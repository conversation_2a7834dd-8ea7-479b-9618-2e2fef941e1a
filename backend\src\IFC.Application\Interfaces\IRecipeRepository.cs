using IFC.Domain.Entities;

namespace IFC.Application.Interfaces;

/// <summary>
/// Repository interface for Recipe entity
/// </summary>
public interface IRecipeRepository
{
    /// <summary>
    /// Gets a recipe by ID
    /// </summary>
    /// <param name="id">Recipe ID</param>
    /// <returns>Recipe or null if not found</returns>
    Task<Recipe?> GetByIdAsync(int id);
    
    /// <summary>
    /// Gets a recipe by ID with ingredients loaded
    /// </summary>
    /// <param name="id">Recipe ID</param>
    /// <returns>Recipe with ingredients or null if not found</returns>
    Task<Recipe?> GetByIdWithIngredientsAsync(int id);
    
    /// <summary>
    /// Gets the default recipe for a product
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <returns>Default recipe or null if not found</returns>
    Task<Recipe?> GetByProductIdAsync(int productId);
    
    /// <summary>
    /// Gets all recipes for a product
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <returns>List of recipes for the product</returns>
    Task<IEnumerable<Recipe>> GetAllByProductIdAsync(int productId);
    
    /// <summary>
    /// Gets all active recipes
    /// </summary>
    /// <returns>List of active recipes</returns>
    Task<IEnumerable<Recipe>> GetAllActiveAsync();
    
    /// <summary>
    /// Gets recipes that use a specific product as an ingredient
    /// </summary>
    /// <param name="ingredientProductId">Product ID used as ingredient</param>
    /// <returns>Recipes that use the product</returns>
    Task<IEnumerable<Recipe>> GetRecipesUsingProductAsync(int ingredientProductId);
    
    /// <summary>
    /// Gets recipes with nested recipe dependencies
    /// </summary>
    /// <returns>Recipes that contain other recipes as ingredients</returns>
    Task<IEnumerable<Recipe>> GetRecipesWithNestedDependenciesAsync();
    
    /// <summary>
    /// Adds a new recipe
    /// </summary>
    /// <param name="recipe">Recipe to add</param>
    /// <returns>Added recipe with ID</returns>
    Task<Recipe> AddAsync(Recipe recipe);
    
    /// <summary>
    /// Updates an existing recipe
    /// </summary>
    /// <param name="recipe">Recipe to update</param>
    /// <returns>Updated recipe</returns>
    Task<Recipe> UpdateAsync(Recipe recipe);
    
    /// <summary>
    /// Deletes a recipe (soft delete by setting IsActive = false)
    /// </summary>
    /// <param name="id">Recipe ID to delete</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteAsync(int id);
    
    /// <summary>
    /// Validates recipe for circular dependencies
    /// </summary>
    /// <param name="recipeId">Recipe ID to validate</param>
    /// <returns>True if recipe has circular dependencies</returns>
    Task<bool> HasCircularDependenciesAsync(int recipeId);
    
    /// <summary>
    /// Gets recipe hierarchy depth
    /// </summary>
    /// <param name="recipeId">Recipe ID</param>
    /// <returns>Maximum nesting depth</returns>
    Task<int> GetRecipeDepthAsync(int recipeId);
}
