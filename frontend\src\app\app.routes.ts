import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },
  {
    path: 'products',
    loadComponent: () => import('./features/products/product-list/product-list.component').then(m => m.ProductListComponent)
  },
  {
    path: 'products/create',
    loadComponent: () => import('./features/products/product-form/product-form.component').then(m => m.ProductFormComponent)
  },
  {
    path: 'products/:id/edit',
    loadComponent: () => import('./features/products/product-form/product-form.component').then(m => m.ProductFormComponent)
  },
  {
    path: 'recipes',
    loadComponent: () => import('./features/recipes/recipe-list/recipe-list.component').then(m => m.RecipeListComponent)
  },
  {
    path: 'recipes/create',
    loadComponent: () => import('./features/recipes/recipe-editor/recipe-editor.component').then(m => m.RecipeEditorComponent)
  },
  {
    path: 'recipes/:id/edit',
    loadComponent: () => import('./features/recipes/recipe-editor/recipe-editor.component').then(m => m.RecipeEditorComponent)
  },
  {
    path: 'recipes/:id/view',
    loadComponent: () => import('./features/recipes/recipe-viewer/recipe-viewer.component').then(m => m.RecipeViewerComponent)
  },
  {
    path: 'production',
    loadComponent: () => import('./features/production/production-list/production-list.component').then(m => m.ProductionListComponent)
  },
  {
    path: 'production/create',
    loadComponent: () => import('./features/production/production-order-form/production-order-form.component').then(m => m.ProductionOrderFormComponent)
  },
  {
    path: 'production/:id/view',
    loadComponent: () => import('./features/production/production-order-detail/production-order-detail.component').then(m => m.ProductionOrderDetailComponent)
  },
  {
    path: 'inventory',
    loadComponent: () => import('./features/inventory/inventory-dashboard/inventory-dashboard.component').then(m => m.InventoryDashboardComponent)
  },
  {
    path: 'inventory/:productId',
    loadComponent: () => import('./features/inventory/inventory-detail/inventory-detail.component').then(m => m.InventoryDetailComponent)
  },
  {
    path: 'reports/production',
    loadComponent: () => import('./features/reports/production-reports/production-reports.component').then(m => m.ProductionReportsComponent)
  },
  {
    path: 'reports/inventory',
    loadComponent: () => import('./features/reports/inventory-reports/inventory-reports.component').then(m => m.InventoryReportsComponent)
  },
  {
    path: '**',
    loadComponent: () => import('./shared/components/not-found/not-found.component').then(m => m.NotFoundComponent)
  }
];
