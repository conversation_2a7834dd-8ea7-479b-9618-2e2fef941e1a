using System.ComponentModel.DataAnnotations;
using IFC.Domain.Enums;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents a production order for manufacturing products
/// </summary>
public class ProductionOrder
{
    [Key]
    public int ProductionOrderId { get; set; }
    
    [Required]
    [StringLength(50)]
    public string OrderNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// Product to be produced
    /// </summary>
    public int ProductId { get; set; }
    
    /// <summary>
    /// Recipe to use for production
    /// </summary>
    public int RecipeId { get; set; }
    
    /// <summary>
    /// Quantity to produce
    /// </summary>
    public decimal QuantityToProduce { get; set; }
    
    /// <summary>
    /// Quantity actually produced
    /// </summary>
    public decimal QuantityProduced { get; set; } = 0;
    
    /// <summary>
    /// Unit of measure for quantities
    /// </summary>
    [StringLength(50)]
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Current status of the production order
    /// </summary>
    public ProductionOrderStatus Status { get; set; } = ProductionOrderStatus.Planned;
    
    /// <summary>
    /// Priority level (1 = highest, 10 = lowest)
    /// </summary>
    public int Priority { get; set; } = 5;
    
    /// <summary>
    /// Cost center where production will take place
    /// </summary>
    public int CostCenterId { get; set; }
    
    [StringLength(200)]
    public string CostCenterName { get; set; } = string.Empty;
    
    /// <summary>
    /// Planned start date for production
    /// </summary>
    public DateTime PlannedStartDate { get; set; }
    
    /// <summary>
    /// Planned completion date for production
    /// </summary>
    public DateTime PlannedEndDate { get; set; }
    
    /// <summary>
    /// Actual start date of production
    /// </summary>
    public DateTime? ActualStartDate { get; set; }
    
    /// <summary>
    /// Actual completion date of production
    /// </summary>
    public DateTime? ActualEndDate { get; set; }
    
    /// <summary>
    /// Estimated total cost for this production order
    /// </summary>
    public decimal EstimatedCost { get; set; }
    
    /// <summary>
    /// Actual total cost for this production order
    /// </summary>
    public decimal ActualCost { get; set; } = 0;
    
    /// <summary>
    /// Notes or comments about this production order
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Parent production order if this is a sub-production for nested recipes
    /// </summary>
    public int? ParentProductionOrderId { get; set; }
    
    /// <summary>
    /// Hierarchy level in nested production (0 = top level)
    /// </summary>
    public int HierarchyLevel { get; set; } = 0;
    
    /// <summary>
    /// Indicates if this order was automatically created for nested recipe processing
    /// </summary>
    public bool IsAutoGenerated { get; set; } = false;
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    public int CreatedBy { get; set; }
    
    public int? ModifiedBy { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// Product to be produced
    /// </summary>
    public virtual Product Product { get; set; } = null!;
    
    /// <summary>
    /// Recipe used for production
    /// </summary>
    public virtual Recipe Recipe { get; set; } = null!;
    
    /// <summary>
    /// Parent production order for nested recipes
    /// </summary>
    public virtual ProductionOrder? ParentProductionOrder { get; set; }
    
    /// <summary>
    /// Child production orders for nested recipes
    /// </summary>
    public virtual ICollection<ProductionOrder> ChildProductionOrders { get; set; } = new List<ProductionOrder>();
    
    /// <summary>
    /// Production steps for this order
    /// </summary>
    public virtual ICollection<ProductionStep> ProductionSteps { get; set; } = new List<ProductionStep>();
    
    /// <summary>
    /// Material consumption records for this order
    /// </summary>
    public virtual ICollection<MaterialConsumption> MaterialConsumptions { get; set; } = new List<MaterialConsumption>();
    
    // Calculated properties
    
    /// <summary>
    /// Completion percentage (0-100)
    /// </summary>
    public decimal CompletionPercentage
    {
        get
        {
            if (QuantityToProduce <= 0) return 0;
            return Math.Min(100, (QuantityProduced / QuantityToProduce) * 100);
        }
    }
    
    /// <summary>
    /// Indicates if this production order is complete
    /// </summary>
    public bool IsComplete => Status == ProductionOrderStatus.Completed;
    
    /// <summary>
    /// Indicates if this production order can be started
    /// </summary>
    public bool CanStart => Status == ProductionOrderStatus.Planned && 
                           (ParentProductionOrder == null || ParentProductionOrder.IsComplete);
}
