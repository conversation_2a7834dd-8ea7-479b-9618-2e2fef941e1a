namespace IFC.WebAPI.DTOs;

/// <summary>
/// DTO for creating production orders
/// </summary>
public class CreateProductionOrderDto
{
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public int CostCenterId { get; set; }
    public DateTime? RequestedDate { get; set; }
    public DateTime? PlannedStartDate { get; set; }
    public string? Notes { get; set; }
    public int Priority { get; set; } = 1;
}

/// <summary>
/// DTO for production order response
/// </summary>
public class ProductionOrderDto
{
    public int ProductionOrderId { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public DateTime? ScheduledStartDate { get; set; }
    public DateTime? ScheduledEndDate { get; set; }
    public DateTime? ActualStartDate { get; set; }
    public DateTime? ActualEndDate { get; set; }
    public decimal EstimatedCost { get; set; }
    public decimal? ActualCost { get; set; }
    public string? Notes { get; set; }
    public int Priority { get; set; }
    public int? ParentProductionOrderId { get; set; }
    public List<ProductionOrderDto> ChildOrders { get; set; } = new();
}

/// <summary>
/// DTO for validating production orders
/// </summary>
public class ValidateProductionOrderDto
{
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public int CostCenterId { get; set; }
    public DateTime? RequestedDate { get; set; }
}

/// <summary>
/// DTO for production validation result
/// </summary>
public class ProductionValidationResultDto
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<MaterialIssueDto> MaterialIssues { get; set; } = new();
    public decimal EstimatedCost { get; set; }
    public int EstimatedDurationMinutes { get; set; }
}

/// <summary>
/// DTO for material issues
/// </summary>
public class MaterialIssueDto
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal RequiredQuantity { get; set; }
    public decimal AvailableQuantity { get; set; }
    public decimal ShortageQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
}

/// <summary>
/// DTO for production hierarchy
/// </summary>
public class ProductionHierarchyDto
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int HierarchyLevel { get; set; }
    public bool IsRawMaterial { get; set; }
    public bool RequiresProduction { get; set; }
    public List<ProductionHierarchyDto> Children { get; set; } = new();
    public decimal EstimatedCost { get; set; }
    public int EstimatedTimeMinutes { get; set; }
}

/// <summary>
/// DTO for material requirements
/// </summary>
public class MaterialRequirementsDto
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public List<MaterialRequirementDto> Requirements { get; set; } = new();
    public decimal TotalEstimatedCost { get; set; }
}

/// <summary>
/// DTO for individual material requirement
/// </summary>
public class MaterialRequirementDto
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal RequiredQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public bool IsRawMaterial { get; set; }
    public bool RequiresProduction { get; set; }
    public int HierarchyLevel { get; set; }
    public decimal UnitCost { get; set; }
    public decimal TotalCost { get; set; }
}

/// <summary>
/// DTO for starting production
/// </summary>
public class StartProductionDto
{
    public DateTime? ActualStartDate { get; set; }
    public string? Notes { get; set; }
    public int UserId { get; set; }
}

/// <summary>
/// DTO for completing production
/// </summary>
public class CompleteProductionDto
{
    public DateTime? ActualEndDate { get; set; }
    public decimal? ActualQuantityProduced { get; set; }
    public decimal? ActualCost { get; set; }
    public string? Notes { get; set; }
    public int UserId { get; set; }
}

/// <summary>
/// DTO for canceling production
/// </summary>
public class CancelProductionDto
{
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public int UserId { get; set; }
}

/// <summary>
/// DTO for material reservation result
/// </summary>
public class MaterialReservationResultDto
{
    public bool IsSuccessful { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<MaterialReservationDto> Reservations { get; set; } = new();
    public decimal TotalReservedValue { get; set; }
}

/// <summary>
/// DTO for material reservation
/// </summary>
public class MaterialReservationDto
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public decimal ReservedQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public DateTime ReservationDate { get; set; }
    public string ReservationReference { get; set; } = string.Empty;
}
