import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTreeModule } from '@angular/material/tree';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { NestedTreeControl } from '@angular/cdk/tree';
import { MatTreeNestedDataSource } from '@angular/material/tree';

import { Product, ProductionOrder } from '../../../core/models/product.model';
import { ProductionService } from '../../../core/services/production.service';
import { ProductService } from '../../../core/services/product.service';
import { ProductionValidationResult, ProductionHierarchy, MaterialRequirement } from '../../../core/models/production.model';

@Component({
  selector: 'app-production-order-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    MatTreeModule,
    MatTooltipModule,
    MatChipsModule
  ],
  template: `
    <div class="production-order-container">
      <mat-card class="header-card">
        <mat-card-header>
          <mat-card-title>Create Production Order</mat-card-title>
          <mat-card-subtitle>
            Create a new production order with automatic nested recipe processing
          </mat-card-subtitle>
        </mat-card-header>
      </mat-card>

      <div class="form-layout">
        <!-- Order Form -->
        <mat-card class="order-form-card">
          <mat-card-header>
            <mat-card-title>Order Details</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <form [formGroup]="orderForm" class="order-form">
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Product</mat-label>
                  <mat-select formControlName="productId" required (selectionChange)="onProductChange()">
                    <mat-option *ngFor="let product of availableProducts" [value]="product.productId">
                      <div class="product-option">
                        <span class="product-code">{{ product.productCode }}</span>
                        <span class="product-name">{{ product.productName }}</span>
                        <mat-chip-set class="product-tags">
                          <mat-chip *ngIf="product.isRecipe" color="accent">Recipe</mat-chip>
                          <mat-chip *ngIf="product.isProduction" color="primary">Production</mat-chip>
                        </mat-chip-set>
                      </div>
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="orderForm.get('productId')?.hasError('required')">
                    Product is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Quantity</mat-label>
                  <input matInput type="number" formControlName="quantity" required min="0.01" step="0.01"
                         (input)="onQuantityChange()">
                  <mat-error *ngIf="orderForm.get('quantity')?.hasError('required')">
                    Quantity is required
                  </mat-error>
                  <mat-error *ngIf="orderForm.get('quantity')?.hasError('min')">
                    Quantity must be greater than 0
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Cost Center</mat-label>
                  <mat-select formControlName="costCenterId" required>
                    <mat-option *ngFor="let center of costCenters" [value]="center.id">
                      {{ center.name }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Planned Start Date</mat-label>
                  <input matInput [matDatepicker]="picker" formControlName="plannedStartDate" required>
                  <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Priority</mat-label>
                  <mat-select formControlName="priority">
                    <mat-option [value]="1">Urgent (1)</mat-option>
                    <mat-option [value]="2">High (2)</mat-option>
                    <mat-option [value]="3">Medium (3)</mat-option>
                    <mat-option [value]="4">Normal (4)</mat-option>
                    <mat-option [value]="5">Low (5)</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Notes</mat-label>
                <textarea matInput formControlName="notes" rows="3"></textarea>
              </mat-form-field>

              <div class="validation-actions">
                <button mat-raised-button color="accent" (click)="validateOrder()" 
                        [disabled]="!canValidate() || isValidating">
                  <mat-spinner diameter="20" *ngIf="isValidating"></mat-spinner>
                  <mat-icon *ngIf="!isValidating">check_circle</mat-icon>
                  Validate Order
                </button>
              </div>
            </form>
          </mat-card-content>
        </mat-card>

        <!-- Production Hierarchy -->
        <mat-card class="hierarchy-card" *ngIf="productionHierarchy">
          <mat-card-header>
            <mat-card-title>Production Hierarchy</mat-card-title>
            <mat-card-subtitle>
              Nested recipe structure and dependencies
            </mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <mat-tree [dataSource]="hierarchyDataSource" [treeControl]="hierarchyTreeControl" class="hierarchy-tree">
              <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
                <div class="hierarchy-node raw-material">
                  <mat-icon class="node-icon" color="primary">inventory</mat-icon>
                  <span class="node-name">{{ node.productName }}</span>
                  <span class="node-code">({{ node.productCode }})</span>
                  <span class="node-quantity">{{ node.quantity }} {{ node.unit }}</span>
                  <span class="node-cost">\${{ node.estimatedCost.toFixed(2) }}</span>
                  <span class="level-indicator">Level {{ node.hierarchyLevel }}</span>
                </div>
              </mat-tree-node>

              <mat-tree-node *matTreeNodeDef="let node; when: hasHierarchyChild" matTreeNodePadding>
                <div class="hierarchy-node recipe-node">
                  <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.productName">
                    <mat-icon class="mat-icon-rtl-mirror">
                      {{ hierarchyTreeControl.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
                    </mat-icon>
                  </button>
                  <mat-icon class="node-icon" color="accent">restaurant</mat-icon>
                  <span class="node-name">{{ node.productName }}</span>
                  <span class="node-code">({{ node.productCode }})</span>
                  <span class="node-quantity">{{ node.quantity }} {{ node.unit }}</span>
                  <span class="node-cost">\${{ node.estimatedCost.toFixed(2) }}</span>
                  <span class="level-indicator">Level {{ node.hierarchyLevel }}</span>
                  <mat-icon class="recipe-indicator" matTooltip="Recipe with nested ingredients">account_tree</mat-icon>
                </div>
              </mat-tree-node>
            </mat-tree>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Validation Results -->
      <mat-card class="validation-card" *ngIf="validationResult">
        <mat-card-header>
          <mat-card-title>
            <mat-icon [color]="validationResult.isValid ? 'primary' : 'warn'">
              {{ validationResult.isValid ? 'check_circle' : 'error' }}
            </mat-icon>
            Validation Results
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <!-- Summary -->
          <div class="validation-summary">
            <div class="summary-grid">
              <div class="summary-item">
                <span class="label">Status:</span>
                <span class="value" [class.success]="validationResult.isValid" [class.error]="!validationResult.isValid">
                  {{ validationResult.isValid ? 'Valid' : 'Invalid' }}
                </span>
              </div>
              <div class="summary-item">
                <span class="label">Estimated Cost:</span>
                <span class="value">\${{ validationResult.estimatedCost.toFixed(2) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">Estimated Duration:</span>
                <span class="value">{{ validationResult.estimatedDurationMinutes }} minutes</span>
              </div>
              <div class="summary-item">
                <span class="label">Completion Date:</span>
                <span class="value">{{ validationResult.estimatedCompletionDate | date:'medium' }}</span>
              </div>
            </div>
          </div>

          <!-- Errors -->
          <mat-expansion-panel *ngIf="validationResult.errors.length > 0" class="error-panel">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <mat-icon color="warn">error</mat-icon>
                Errors ({{ validationResult.errors.length }})
              </mat-panel-title>
            </mat-expansion-panel-header>
            <div class="message-list">
              <div class="message error" *ngFor="let error of validationResult.errors">
                <mat-icon>error</mat-icon>
                <span>{{ error }}</span>
              </div>
            </div>
          </mat-expansion-panel>

          <!-- Warnings -->
          <mat-expansion-panel *ngIf="validationResult.warnings.length > 0" class="warning-panel">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <mat-icon color="accent">warning</mat-icon>
                Warnings ({{ validationResult.warnings.length }})
              </mat-panel-title>
            </mat-expansion-panel-header>
            <div class="message-list">
              <div class="message warning" *ngFor="let warning of validationResult.warnings">
                <mat-icon>warning</mat-icon>
                <span>{{ warning }}</span>
              </div>
            </div>
          </mat-expansion-panel>

          <!-- Material Issues -->
          <mat-expansion-panel *ngIf="validationResult.materialIssues.length > 0" class="material-panel">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <mat-icon color="warn">inventory</mat-icon>
                Material Issues ({{ validationResult.materialIssues.length }})
              </mat-panel-title>
            </mat-expansion-panel-header>
            <div class="material-issues">
              <div class="material-issue" *ngFor="let issue of validationResult.materialIssues">
                <div class="issue-header">
                  <span class="product-info">{{ issue.productCode }} - {{ issue.productName }}</span>
                  <span class="issue-type">{{ issue.issueType }}</span>
                </div>
                <div class="issue-details">
                  <span>Required: {{ issue.requiredQuantity }} {{ issue.unit }}</span>
                  <span>Available: {{ issue.availableQuantity }} {{ issue.unit }}</span>
                  <span class="shortage">Shortage: {{ issue.shortageQuantity }} {{ issue.unit }}</span>
                </div>
                <div class="suggestion" *ngIf="issue.suggestion">
                  <mat-icon>lightbulb</mat-icon>
                  <span>{{ issue.suggestion }}</span>
                </div>
              </div>
            </div>
          </mat-expansion-panel>
        </mat-card-content>
      </mat-card>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button mat-button (click)="cancel()">Cancel</button>
        <button mat-raised-button color="primary" (click)="createOrder()" 
                [disabled]="!canCreateOrder()" class="create-button">
          <mat-icon>add</mat-icon>
          Create Production Order
        </button>
      </div>
    </div>
  `,
  styles: [`
    .production-order-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
    }

    .header-card {
      margin-bottom: 20px;
    }

    .form-layout {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;
    }

    .order-form-card,
    .hierarchy-card {
      height: fit-content;
    }

    .order-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .form-row mat-form-field {
      flex: 1;
    }

    .full-width {
      width: 100%;
    }

    .product-option {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .product-code {
      font-weight: 500;
      color: #1976d2;
    }

    .product-name {
      font-size: 0.9em;
      color: #666;
    }

    .product-tags {
      margin-top: 4px;
    }

    .validation-actions {
      display: flex;
      justify-content: center;
      margin-top: 16px;
    }

    .hierarchy-tree {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      max-height: 400px;
      overflow-y: auto;
    }

    .hierarchy-node {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      min-height: 48px;
    }

    .hierarchy-node.recipe-node {
      background-color: #f3e5f5;
    }

    .hierarchy-node.raw-material {
      background-color: #e8f5e8;
    }

    .node-icon {
      margin-right: 8px;
    }

    .node-name {
      font-weight: 500;
      margin-right: 8px;
    }

    .node-code {
      color: #666;
      font-size: 0.9em;
      margin-right: 12px;
    }

    .node-quantity {
      margin-right: 12px;
      color: #666;
    }

    .node-cost {
      margin-right: 12px;
      font-weight: 500;
      color: #2e7d32;
    }

    .level-indicator {
      margin-right: 8px;
      font-size: 0.8em;
      background-color: #e3f2fd;
      padding: 2px 6px;
      border-radius: 12px;
      color: #1976d2;
    }

    .recipe-indicator {
      color: #9c27b0;
    }

    .validation-card {
      margin-bottom: 20px;
    }

    .validation-summary {
      margin-bottom: 16px;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .summary-item .label {
      font-weight: 500;
    }

    .summary-item .value.success {
      color: #2e7d32;
      font-weight: 600;
    }

    .summary-item .value.error {
      color: #c62828;
      font-weight: 600;
    }

    .error-panel,
    .warning-panel,
    .material-panel {
      margin: 8px 0;
    }

    .message-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .message {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 4px;
    }

    .message.error {
      background-color: #ffebee;
      color: #c62828;
    }

    .message.warning {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .material-issues {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .material-issue {
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background-color: #fafafa;
    }

    .issue-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .product-info {
      font-weight: 500;
    }

    .issue-type {
      background-color: #ffcdd2;
      color: #c62828;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.8em;
    }

    .issue-details {
      display: flex;
      gap: 16px;
      margin-bottom: 8px;
      font-size: 0.9em;
    }

    .shortage {
      color: #c62828;
      font-weight: 500;
    }

    .suggestion {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1976d2;
      font-size: 0.9em;
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }

    .create-button {
      background-color: #2e7d32 !important;
    }

    @media (max-width: 1200px) {
      .form-layout {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ProductionOrderFormComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);
  private productionService = inject(ProductionService);
  private productService = inject(ProductService);

  orderForm!: FormGroup;
  availableProducts: Product[] = [];
  costCenters = [
    { id: 1, name: 'Main Production' },
    { id: 2, name: 'Secondary Production' },
    { id: 3, name: 'Quality Control' }
  ];

  isValidating = false;
  validationResult?: ProductionValidationResult;
  productionHierarchy?: ProductionHierarchy;

  // Tree control for production hierarchy
  hierarchyTreeControl = new NestedTreeControl<ProductionHierarchy>(node => node.children);
  hierarchyDataSource = new MatTreeNestedDataSource<ProductionHierarchy>();

  ngOnInit() {
    this.initializeForm();
    this.loadAvailableProducts();
  }

  private initializeForm() {
    this.orderForm = this.fb.group({
      productId: ['', Validators.required],
      quantity: [1, [Validators.required, Validators.min(0.01)]],
      costCenterId: [1, Validators.required],
      plannedStartDate: [new Date(), Validators.required],
      priority: [5],
      notes: ['']
    });
  }

  private async loadAvailableProducts() {
    try {
      this.availableProducts = await this.productService.getProductionProducts();
    } catch (error) {
      this.snackBar.open('Failed to load products', 'Close', { duration: 3000 });
    }
  }

  onProductChange() {
    this.validationResult = undefined;
    this.productionHierarchy = undefined;
  }

  onQuantityChange() {
    this.validationResult = undefined;
    this.productionHierarchy = undefined;
  }

  canValidate(): boolean {
    return this.orderForm.get('productId')?.valid && this.orderForm.get('quantity')?.valid && this.orderForm.get('costCenterId')?.valid;
  }

  async validateOrder() {
    if (!this.canValidate()) return;

    this.isValidating = true;
    try {
      const formValue = this.orderForm.value;
      
      // Get production hierarchy
      this.productionHierarchy = await this.productionService.getProductionHierarchy(
        formValue.productId, 
        formValue.quantity
      );
      
      this.hierarchyDataSource.data = [this.productionHierarchy];
      this.hierarchyTreeControl.expandAll();

      // Validate the order
      this.validationResult = await this.productionService.validateProductionOrder(
        formValue.productId,
        formValue.quantity,
        formValue.costCenterId
      );

      if (this.validationResult.isValid) {
        this.snackBar.open('Order validation passed!', 'Close', { duration: 3000 });
      } else {
        this.snackBar.open('Order validation failed - check issues below', 'Close', { duration: 5000 });
      }
    } catch (error) {
      this.snackBar.open('Failed to validate order', 'Close', { duration: 3000 });
    } finally {
      this.isValidating = false;
    }
  }

  hasHierarchyChild = (_: number, node: ProductionHierarchy) => !!node.children && node.children.length > 0;

  canCreateOrder(): boolean {
    return this.orderForm.valid && 
           this.validationResult?.isValid === true;
  }

  async createOrder() {
    if (!this.canCreateOrder()) return;

    try {
      const formValue = this.orderForm.value;
      const productionOrder = await this.productionService.createProductionOrder(
        formValue.productId,
        formValue.quantity,
        formValue.costCenterId,
        formValue.plannedStartDate,
        formValue.priority
      );

      this.snackBar.open('Production order created successfully!', 'Close', { duration: 3000 });
      this.router.navigate(['/production', productionOrder.productionOrderId, 'view']);
    } catch (error) {
      this.snackBar.open('Failed to create production order', 'Close', { duration: 3000 });
    }
  }

  cancel() {
    this.router.navigate(['/production']);
  }
}
