﻿Imports System.Data
Imports System.Data.SqlClient

Public Class Setting_Cls
    Dim Conn As New Conn_Cls

    Public Function GetIdSetting() As Integer
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = Conn.SELECT_TXT("select isnull(Max(Ser)+1,1) as ID from  POSSetting ")
        Return Convert.ToInt32(Dt.Rows(0)("ID"))

    End Function

    Public Function LoadFGData() As DataTable
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = Conn.SELECT_TXT("select * from  POSSetting where IsDelete=0 ")
        Return Dt

    End Function
    Public Function SaveEditSetting(Ser As Integer, Company_IdPOS As Integer, Company_NamePOS As String,
                               Brand_IdPOS As Integer, Brand_NamePOS As String, CostCenter_IdPOS As Integer,
                               CostCenter_NamePOS As String, Optional IsSave As Boolean = False) As Integer
        Dim SqlStr As String = ""
        If IsSave Then
            Ser = GetIdSetting()

            SqlStr = " INSERT INTO POSSetting (Ser , "
            SqlStr = SqlStr & "   Company_IdPOS,Company_NamePOS,Brand_IdPOS,Brand_NamePOS,CostCenter_IdPOS,CostCenter_NamePOS)"
            SqlStr = SqlStr & "  VALUES "

            SqlStr = SqlStr & "  (" & Ser & "  "
            SqlStr = SqlStr & "  ," & Company_IdPOS & ",'" & Company_NamePOS & "'," & Brand_IdPOS & ",'" & Brand_NamePOS & "'," & CostCenter_IdPOS & ",'" & CostCenter_NamePOS & "')"

        Else

            SqlStr = " Update POSSetting set  "
            SqlStr = SqlStr & "  Company_IdPOS=" & Company_IdPOS & ",Company_NamePOS='" & Company_NamePOS & "',Brand_IdPOS=" & Brand_IdPOS & ",Brand_NamePOS='" & Brand_NamePOS & "',CostCenter_IdPOS=" & CostCenter_IdPOS & ",CostCenter_NamePOS='" & CostCenter_NamePOS & "' "

            SqlStr = SqlStr & " where Ser=" & Ser & ""
        End If


        Conn.EXECUT_Txt(SqlStr)
        Return Ser
    End Function
    Public Function SaveEditSetting1(Ser As Integer, Company_IdSCM As Integer, Company_NameSCM As String,
                               Branch_IdSCM As Integer, Branch_NameSCM As String, Store_IdSCM As Integer,
                               Store_NameSCM As String, Company_IdPOS As Integer, Company_NamePOS As String,
                               Brand_IdPOS As Integer, Brand_NamePOS As String, CostCenter_IdPOS As Integer,
                               CostCenter_NamePOS As String, Optional IsSave As Boolean = False) As Integer
        Dim SqlStr As String = ""
        If IsSave Then
            Ser = GetIdSetting()

            SqlStr = " INSERT INTO POSSetting (Ser ,Company_IdSCM,Company_NameSCM,Branch_IdSCM ,Branch_NameSCM,Store_IdSCM"
            SqlStr = SqlStr & "  ,Store_NameSCM ,Company_IdPOS,Company_NamePOS,Brand_IdPOS,Brand_NamePOS,CostCenter_IdPOS,CostCenter_NamePOS)"
            SqlStr = SqlStr & "  VALUES "

            SqlStr = SqlStr & "  (" & Ser & " ," & Company_IdSCM & ",'" & Company_NameSCM & "'," & Branch_IdSCM & ",'" & Branch_NameSCM & "'," & Store_IdSCM & ""
            SqlStr = SqlStr & "  ,'" & Store_NameSCM & "'," & Company_IdPOS & ",'" & Company_NamePOS & "'," & Brand_IdPOS & ",'" & Brand_NamePOS & "'," & CostCenter_IdPOS & ",'" & CostCenter_NamePOS & "')"

        Else

            SqlStr = " Update POSSetting set Company_IdSCM=" & Company_IdSCM & ",Company_NameSCM='" & Company_NameSCM & "',Branch_IdSCM =" & Branch_IdSCM & ",Branch_NameSCM='" & Branch_NameSCM & "',Store_IdSCM=" & Store_IdSCM & ""
            SqlStr = SqlStr & "  ,Store_NameSCM='" & Store_NameSCM & "' ,Company_IdPOS=" & Company_IdPOS & ",Company_NamePOS='" & Company_NamePOS & "',Brand_IdPOS=" & Brand_IdPOS & ",Brand_NamePOS='" & Brand_NamePOS & "',CostCenter_IdPOS=" & CostCenter_IdPOS & ",CostCenter_NamePOS='" & CostCenter_NamePOS & "' "

            SqlStr = SqlStr & " where Ser=" & Ser & ""
        End If


        Conn.EXECUT_Txt(SqlStr)
        Return Ser
    End Function
    Public Sub DeleteSetting(ID As Integer)
        Conn.EXECUT_Txt("Delete From POSSetting where Ser=" & ID & "")
        DeleteCostLink(ID)
    End Sub
    Public Function FillCompany() As DataTable
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = Conn.SELECT_TXT("SELECT  Comp_Id, Comp_Name FROM     CompanyTbl")
        Return Dt

    End Function

    Public Function FillBranch(CompanyId As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = Conn.SELECT_TXT("SELECT  Location_Id, Location_Name FROM     LocationTbl where Comp_Id=" & CompanyId & "")
        Return Dt

    End Function

    Public Function FillStore(BranchId As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = Conn.SELECT_TXT("SELECT  Store_id, Store_Name FROM      StoresTbl where Location_Id=" & BranchId & "")
        Return Dt

    End Function
    Public Function Show_()
        Dim DT As New DataTable

        DT.Clear()

        DT = Conn.SELECT_TXT("SELECT CostCenter_Id, CostCenter_Name, Store_id, Store_Name,Type_id,CostCenter_Type FROM    CostCenterTbl")
        Return DT

    End Function

    Public Function GetCostDataLink(Ser As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = Conn.SELECT_TXT("select * from CostCenterLinkPOS where Ser=" & Ser & "")
        Return Dt
    End Function
    Public Sub DeleteCostLink(Ser As Integer)

        Conn.EXECUT_Txt("Delete from CostCenterLinkPOS where Ser=" & Ser & "")
    End Sub
    Public Sub SaveCostData(Ser As Integer, CostCenter_Id As Integer, CostCenter_Name As String)

        Dim Sql As String = ""
        Sql = "INSERT INTO CostCenterLinkPOS  (Ser,CostCenter_Id,CostCenter_Name) VALUES "
        Sql = Sql & " (" & Ser & "," & CostCenter_Id & ",'" & CostCenter_Name & "')"

        Conn.EXECUT_Txt(Sql)
    End Sub

    Public Function GetCostDataLink() As DataTable
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = Conn.SELECT_TXT("select * from CostCenterLinkPOS order by CostCenter_Id")
        Return Dt
    End Function
End Class
