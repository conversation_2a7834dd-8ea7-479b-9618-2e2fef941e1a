import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, firstValueFrom } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Recipe, RecipeIngredient } from '../models/product.model';

@Injectable({
  providedIn: 'root'
})
export class RecipeService {
  private http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/recipes`;

  /**
   * Gets a recipe by ID with all ingredients
   * Replicates VB.NET: ShowWithProduct_IdAll() but with enhanced structure
   */
  async getRecipeWithIngredients(recipeId: number): Promise<Recipe | null> {
    try {
      return await firstValueFrom(
        this.http.get<Recipe>(`${this.apiUrl}/${recipeId}/with-ingredients`)
      );
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Gets recipe by product ID
   */
  async getRecipeByProductId(productId: number): Promise<Recipe | null> {
    try {
      return await firstValueFrom(
        this.http.get<Recipe>(`${this.apiUrl}/by-product/${productId}`)
      );
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Gets all recipes
   */
  async getAllRecipes(): Promise<Recipe[]> {
    return firstValueFrom(
      this.http.get<Recipe[]>(`${this.apiUrl}`)
    );
  }

  /**
   * Gets recipes by product name or code
   */
  async searchRecipes(searchTerm: string): Promise<Recipe[]> {
    const params = new HttpParams().set('search', searchTerm);
    
    return firstValueFrom(
      this.http.get<Recipe[]>(`${this.apiUrl}/search`, { params })
    );
  }

  /**
   * Gets active recipes only
   */
  async getActiveRecipes(): Promise<Recipe[]> {
    return firstValueFrom(
      this.http.get<Recipe[]>(`${this.apiUrl}/active`)
    );
  }

  /**
   * Gets recipes that contain a specific ingredient
   */
  async getRecipesUsingIngredient(ingredientProductId: number): Promise<Recipe[]> {
    return firstValueFrom(
      this.http.get<Recipe[]>(`${this.apiUrl}/using-ingredient/${ingredientProductId}`)
    );
  }

  /**
   * Validates a recipe for circular dependencies and other issues
   * Enhanced version of VB.NET validation
   */
  async validateRecipe(recipe: Partial<Recipe>): Promise<RecipeValidationResult> {
    return firstValueFrom(
      this.http.post<RecipeValidationResult>(`${this.apiUrl}/validate`, recipe)
    );
  }

  /**
   * Creates a new recipe
   */
  async createRecipe(recipe: Partial<Recipe>): Promise<Recipe> {
    return firstValueFrom(
      this.http.post<Recipe>(`${this.apiUrl}`, recipe)
    );
  }

  /**
   * Updates an existing recipe
   */
  async updateRecipe(recipeId: number, recipe: Partial<Recipe>): Promise<Recipe> {
    return firstValueFrom(
      this.http.put<Recipe>(`${this.apiUrl}/${recipeId}`, recipe)
    );
  }

  /**
   * Deletes a recipe (soft delete)
   */
  async deleteRecipe(recipeId: number): Promise<boolean> {
    return firstValueFrom(
      this.http.delete<boolean>(`${this.apiUrl}/${recipeId}`)
    );
  }

  /**
   * Adds an ingredient to a recipe
   */
  async addIngredient(recipeId: number, ingredient: Partial<RecipeIngredient>): Promise<RecipeIngredient> {
    return firstValueFrom(
      this.http.post<RecipeIngredient>(`${this.apiUrl}/${recipeId}/ingredients`, ingredient)
    );
  }

  /**
   * Updates a recipe ingredient
   */
  async updateIngredient(recipeId: number, ingredientId: number, ingredient: Partial<RecipeIngredient>): Promise<RecipeIngredient> {
    return firstValueFrom(
      this.http.put<RecipeIngredient>(`${this.apiUrl}/${recipeId}/ingredients/${ingredientId}`, ingredient)
    );
  }

  /**
   * Removes an ingredient from a recipe
   */
  async removeIngredient(recipeId: number, ingredientId: number): Promise<boolean> {
    return firstValueFrom(
      this.http.delete<boolean>(`${this.apiUrl}/${recipeId}/ingredients/${ingredientId}`)
    );
  }

  /**
   * Gets recipe cost breakdown
   */
  async getRecipeCostBreakdown(recipeId: number): Promise<RecipeCostBreakdown> {
    return firstValueFrom(
      this.http.get<RecipeCostBreakdown>(`${this.apiUrl}/${recipeId}/cost-breakdown`)
    );
  }

  /**
   * Calculates recipe cost for a specific quantity
   */
  async calculateRecipeCost(recipeId: number, quantity: number): Promise<RecipeCostCalculation> {
    const params = new HttpParams().set('quantity', quantity.toString());
    
    return firstValueFrom(
      this.http.get<RecipeCostCalculation>(`${this.apiUrl}/${recipeId}/calculate-cost`, { params })
    );
  }

  /**
   * Gets recipe hierarchy (nested recipes)
   */
  async getRecipeHierarchy(recipeId: number): Promise<RecipeHierarchy> {
    return firstValueFrom(
      this.http.get<RecipeHierarchy>(`${this.apiUrl}/${recipeId}/hierarchy`)
    );
  }

  /**
   * Checks for circular dependencies in a recipe
   */
  async checkCircularDependencies(recipeId: number): Promise<CircularDependencyCheck> {
    return firstValueFrom(
      this.http.get<CircularDependencyCheck>(`${this.apiUrl}/${recipeId}/check-circular`)
    );
  }

  /**
   * Gets recipe versions
   */
  async getRecipeVersions(productId: number): Promise<Recipe[]> {
    return firstValueFrom(
      this.http.get<Recipe[]>(`${this.apiUrl}/versions/${productId}`)
    );
  }

  /**
   * Clones a recipe
   */
  async cloneRecipe(recipeId: number, newRecipeName: string): Promise<Recipe> {
    const request = { newRecipeName };
    
    return firstValueFrom(
      this.http.post<Recipe>(`${this.apiUrl}/${recipeId}/clone`, request)
    );
  }

  // Observable versions for reactive programming

  getRecipeWithIngredientsObservable(recipeId: number): Observable<Recipe> {
    return this.http.get<Recipe>(`${this.apiUrl}/${recipeId}/with-ingredients`);
  }

  getAllRecipesObservable(): Observable<Recipe[]> {
    return this.http.get<Recipe[]>(`${this.apiUrl}`);
  }

  searchRecipesObservable(searchTerm: string): Observable<Recipe[]> {
    const params = new HttpParams().set('search', searchTerm);
    return this.http.get<Recipe[]>(`${this.apiUrl}/search`, { params });
  }

  validateRecipeObservable(recipe: Partial<Recipe>): Observable<RecipeValidationResult> {
    return this.http.post<RecipeValidationResult>(`${this.apiUrl}/validate`, recipe);
  }
}

// Supporting interfaces for recipe operations

export interface RecipeValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  circularDependencies: number[];
  maxDepth: number;
}

export interface RecipeCostBreakdown {
  materialCost: number;
  laborCost: number;
  overheadCost: number;
  totalCost: number;
  costPerUnit: number;
  ingredients: IngredientCost[];
}

export interface IngredientCost {
  ingredientId: number;
  productCode: string;
  productName: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  isNestedRecipe: boolean;
}

export interface RecipeCostCalculation {
  quantity: number;
  totalMaterialCost: number;
  totalLaborCost: number;
  totalOverheadCost: number;
  totalCost: number;
  costPerUnit: number;
}

export interface RecipeHierarchy {
  recipeId: number;
  recipeName: string;
  level: number;
  children: RecipeHierarchy[];
  ingredients: RecipeIngredient[];
}

export interface CircularDependencyCheck {
  hasCircularDependency: boolean;
  dependencyChain: number[];
  affectedRecipes: number[];
}
