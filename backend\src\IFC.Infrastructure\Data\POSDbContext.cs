using Microsoft.EntityFrameworkCore;
using IFC.Domain.Entities.POS;

namespace IFC.Infrastructure.Data;

/// <summary>
/// Entity Framework DbContext for POS Database
/// Replicates VB.NET SqlConnPOS connection and operations
/// </summary>
public class POSDbContext : DbContext
{
    public POSDbContext(DbContextOptions<POSDbContext> options) : base(options)
    {
    }

    // POS entities
    public DbSet<POSSetting> POSSettings { get; set; }
    public DbSet<SalesPOS> SalesPOS { get; set; }
    public DbSet<POSTransaction> POSTransactions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure POSSetting entity
        modelBuilder.Entity<POSSetting>(entity =>
        {
            entity.HasKey(e => e.POSSettingId);
            entity.Property(e => e.CompanyNamePOS).IsRequired();
            entity.Property(e => e.BrandNamePOS).IsRequired();
            entity.Property(e => e.CostCenterNamePOS).IsRequired();
            
            // Index for performance (matching VB.NET queries)
            entity.HasIndex(e => new { e.CompanyIdPOS, e.BrandIdPOS, e.CostCenterIdPOS })
                  .HasDatabaseName("IX_POSSetting_CompanyBrandCostCenter");
        });

        // Configure SalesPOS entity
        modelBuilder.Entity<SalesPOS>(entity =>
        {
            entity.HasKey(e => e.SalesPOSId);
            entity.Property(e => e.TransactionCode).IsRequired();
            entity.Property(e => e.ProductCode).IsRequired();
            entity.Property(e => e.ProductName).IsRequired();
            
            // Indexes for performance
            entity.HasIndex(e => e.ProductId)
                  .HasDatabaseName("IX_SalesPOS_ProductId");
            
            entity.HasIndex(e => e.TransactionDateCreate)
                  .HasDatabaseName("IX_SalesPOS_TransactionDate");
            
            entity.HasIndex(e => new { e.CostCenterPOSId, e.TransactionDateCreate })
                  .HasDatabaseName("IX_SalesPOS_CostCenterDate");
        });

        // Configure POSTransaction entity (view)
        modelBuilder.Entity<POSTransaction>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToView("v_ProcIFC"); // This is a view, not a table
            
            // Indexes for performance (if supported by view)
            entity.HasIndex(e => new { e.Mandant, e.Outlet, e.Center })
                  .HasDatabaseName("IX_POSTransaction_MandantOutletCenter");
            
            entity.HasIndex(e => e.StatistDate)
                  .HasDatabaseName("IX_POSTransaction_StatistDate");
            
            entity.HasIndex(e => e.IsIFCDone)
                  .HasDatabaseName("IX_POSTransaction_IsIFCDone");
        });
    }
}
