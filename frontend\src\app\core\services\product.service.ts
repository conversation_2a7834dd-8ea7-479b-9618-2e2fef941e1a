import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, firstValueFrom } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Product } from '../models/product.model';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/products`;

  /**
   * Gets all products that can be used for production (IsProduction=true OR IsRecipe=true)
   * Replicates VB.NET query for production products
   */
  async getProductionProducts(): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/production`)
    );
  }

  /**
   * Gets all recipe products (IsRecipe=true)
   */
  async getRecipeProducts(): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/recipes`)
    );
  }

  /**
   * Gets all raw materials (not recipe, not production)
   */
  async getRawMaterials(): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/raw-materials`)
    );
  }

  /**
   * Gets a product by ID with recipe information
   */
  async getProductById(id: number): Promise<Product | null> {
    try {
      return await firstValueFrom(
        this.http.get<Product>(`${this.apiUrl}/${id}`)
      );
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Gets a product by code
   */
  async getProductByCode(code: string): Promise<Product | null> {
    try {
      return await firstValueFrom(
        this.http.get<Product>(`${this.apiUrl}/by-code/${code}`)
      );
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Searches products by code or name
   */
  async searchProducts(searchTerm: string): Promise<Product[]> {
    const params = new HttpParams().set('search', searchTerm);
    
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/search`, { params })
    );
  }

  /**
   * Gets products by department
   */
  async getProductsByDepartment(departmentId: number): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/department/${departmentId}`)
    );
  }

  /**
   * Gets products by group
   */
  async getProductsByGroup(groupId: number): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/group/${groupId}`)
    );
  }

  /**
   * Gets expirable products
   */
  async getExpirableProducts(): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/expirable`)
    );
  }

  /**
   * Checks if a product has a recipe
   */
  async hasRecipe(productId: number): Promise<boolean> {
    return firstValueFrom(
      this.http.get<boolean>(`${this.apiUrl}/${productId}/has-recipe`)
    );
  }

  /**
   * Checks if a product is used in other recipes
   */
  async isUsedInRecipes(productId: number): Promise<boolean> {
    return firstValueFrom(
      this.http.get<boolean>(`${this.apiUrl}/${productId}/used-in-recipes`)
    );
  }

  /**
   * Gets products that use a specific ingredient
   */
  async getProductsUsingIngredient(ingredientProductId: number): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/using-ingredient/${ingredientProductId}`)
    );
  }

  /**
   * Gets average cost for a product
   */
  async getAverageCost(productId: number): Promise<number> {
    return firstValueFrom(
      this.http.get<number>(`${this.apiUrl}/${productId}/average-cost`)
    );
  }

  /**
   * Creates a new product
   */
  async createProduct(product: Partial<Product>): Promise<Product> {
    return firstValueFrom(
      this.http.post<Product>(`${this.apiUrl}`, product)
    );
  }

  /**
   * Updates an existing product
   */
  async updateProduct(id: number, product: Partial<Product>): Promise<Product> {
    return firstValueFrom(
      this.http.put<Product>(`${this.apiUrl}/${id}`, product)
    );
  }

  /**
   * Deletes a product (soft delete)
   */
  async deleteProduct(id: number): Promise<boolean> {
    return firstValueFrom(
      this.http.delete<boolean>(`${this.apiUrl}/${id}`)
    );
  }

  /**
   * Checks if a product exists
   */
  async productExists(id: number): Promise<boolean> {
    return firstValueFrom(
      this.http.get<boolean>(`${this.apiUrl}/${id}/exists`)
    );
  }

  /**
   * Checks if a product code exists
   */
  async codeExists(code: string, excludeId?: number): Promise<boolean> {
    let params = new HttpParams().set('code', code);
    
    if (excludeId !== undefined) {
      params = params.set('excludeId', excludeId.toString());
    }
    
    return firstValueFrom(
      this.http.get<boolean>(`${this.apiUrl}/code-exists`, { params })
    );
  }

  /**
   * Gets all products with pagination
   */
  async getProducts(page: number = 1, pageSize: number = 50): Promise<Product[]> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());
    
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}`, { params })
    );
  }

  /**
   * Gets total product count
   */
  async getProductCount(): Promise<number> {
    return firstValueFrom(
      this.http.get<number>(`${this.apiUrl}/count`)
    );
  }

  /**
   * Gets products with low stock
   */
  async getLowStockProducts(): Promise<Product[]> {
    return firstValueFrom(
      this.http.get<Product[]>(`${this.apiUrl}/low-stock`)
    );
  }

  // Observable versions for reactive programming

  getProductionProductsObservable(): Observable<Product[]> {
    return this.http.get<Product[]>(`${this.apiUrl}/production`);
  }

  searchProductsObservable(searchTerm: string): Observable<Product[]> {
    const params = new HttpParams().set('search', searchTerm);
    return this.http.get<Product[]>(`${this.apiUrl}/search`, { params });
  }

  getProductByIdObservable(id: number): Observable<Product> {
    return this.http.get<Product>(`${this.apiUrl}/${id}`);
  }

  getProductsObservable(page: number = 1, pageSize: number = 50): Observable<Product[]> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());
    
    return this.http.get<Product[]>(`${this.apiUrl}`, { params });
  }
}
