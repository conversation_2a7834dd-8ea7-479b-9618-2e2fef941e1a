using Microsoft.EntityFrameworkCore;
using IFC.Application.Interfaces;
using IFC.Domain.Entities;
using IFC.Infrastructure.Data;

namespace IFC.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Recipe entity
/// </summary>
public class RecipeRepository : IRecipeRepository
{
    private readonly IFCDbContext _context;

    public RecipeRepository(IFCDbContext context)
    {
        _context = context;
    }

    public async Task<Recipe?> GetByIdAsync(int id)
    {
        return await _context.Recipes
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.IngredientProduct)
            .FirstOrDefaultAsync(r => r.RecipeId == id);
    }

    public async Task<Recipe?> GetByProductIdAsync(int productId)
    {
        return await _context.Recipes
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.IngredientProduct)
            .FirstOrDefaultAsync(r => r.ProductId == productId);
    }

    public async Task<IEnumerable<Recipe>> GetAllAsync()
    {
        return await _context.Recipes
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.IngredientProduct)
            .ToListAsync();
    }

    public async Task<IEnumerable<Recipe>> GetByProductTypeAsync(bool isProduction)
    {
        return await _context.Recipes
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.IngredientProduct)
            .Where(r => r.Product.IsProduction == isProduction)
            .ToListAsync();
    }

    public async Task<Recipe> AddAsync(Recipe recipe)
    {
        _context.Recipes.Add(recipe);
        await _context.SaveChangesAsync();
        return recipe;
    }

    public async Task<Recipe> UpdateAsync(Recipe recipe)
    {
        _context.Recipes.Update(recipe);
        await _context.SaveChangesAsync();
        return recipe;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var recipe = await _context.Recipes.FindAsync(id);
        if (recipe == null) return false;

        recipe.IsActive = false;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ExistsAsync(int productId, int? excludeId = null)
    {
        return await _context.Recipes
            .AnyAsync(r => r.ProductId == productId && 
                          (excludeId == null || r.RecipeId != excludeId));
    }

    public async Task<IEnumerable<Recipe>> GetRecipesUsingIngredientAsync(int ingredientProductId)
    {
        return await _context.Recipes
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.IngredientProduct)
            .Where(r => r.Ingredients.Any(i => i.IngredientProductId == ingredientProductId))
            .ToListAsync();
    }

    public async Task<IEnumerable<Recipe>> GetActiveRecipesAsync()
    {
        return await _context.Recipes
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.IngredientProduct)
            .Where(r => r.IsActive)
            .ToListAsync();
    }

    public async Task<Recipe?> GetRecipeWithFullHierarchyAsync(int recipeId)
    {
        return await _context.Recipes
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.IngredientProduct)
                    .ThenInclude(p => p.Recipe)
                        .ThenInclude(r => r.Ingredients)
            .FirstOrDefaultAsync(r => r.RecipeId == recipeId);
    }
}
