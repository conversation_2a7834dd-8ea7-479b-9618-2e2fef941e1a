using System.ComponentModel.DataAnnotations;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents a product in the system that can be a raw material, recipe, or production item
/// </summary>
public class Product
{
    [Key]
    public int ProductId { get; set; }
    
    [Required]
    [StringLength(50)]
    public string ProductCode { get; set; } = string.Empty;
    
    [Required]
    [StringLength(200)]
    public string ProductName { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string? BrandName { get; set; }
    
    public int UnitId { get; set; }
    
    [StringLength(50)]
    public string UnitName { get; set; } = string.Empty;
    
    public int UnitGroupId { get; set; }
    
    public decimal UnitQuantity { get; set; }
    
    public int? DepartmentId { get; set; }
    
    public int? GroupId { get; set; }
    
    public int? SubGroupId { get; set; }
    
    /// <summary>
    /// Cost per unit of the product
    /// </summary>
    public decimal CostPerUnit { get; set; }
    
    /// <summary>
    /// Average cost calculated from inventory movements
    /// </summary>
    public decimal AverageCost { get; set; }
    
    /// <summary>
    /// Sales price per unit
    /// </summary>
    public decimal SalesPrice { get; set; }
    
    /// <summary>
    /// Minimum stock level for reorder alerts
    /// </summary>
    public decimal MinimumStock { get; set; }
    
    /// <summary>
    /// Maximum stock level
    /// </summary>
    public decimal MaximumStock { get; set; }
    
    /// <summary>
    /// Reorder point quantity
    /// </summary>
    public decimal ReorderPoint { get; set; }
    
    [StringLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Indicates if this product is tracked in inventory
    /// </summary>
    public bool IsStock { get; set; }
    
    /// <summary>
    /// Indicates if this product is a recipe (contains other products)
    /// </summary>
    public bool IsRecipe { get; set; }
    
    /// <summary>
    /// Indicates if this product has expiration dates
    /// </summary>
    public bool IsExpirable { get; set; }
    
    /// <summary>
    /// Indicates if this product is produced (manufactured) rather than purchased
    /// </summary>
    public bool IsProduction { get; set; }
    
    /// <summary>
    /// Indicates if this product can be sold
    /// </summary>
    public bool IsSales { get; set; }
    
    /// <summary>
    /// Authorization level required for this product
    /// </summary>
    public int AuthorizationLevel { get; set; }
    
    /// <summary>
    /// Indicates if the product is active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// Recipe definition if this product is a recipe
    /// </summary>
    public virtual Recipe? Recipe { get; set; }
    
    /// <summary>
    /// Inventory records for this product
    /// </summary>
    public virtual ICollection<Inventory> InventoryRecords { get; set; } = new List<Inventory>();
    
    /// <summary>
    /// Recipe ingredients where this product is used as an ingredient
    /// </summary>
    public virtual ICollection<RecipeIngredient> UsedInRecipes { get; set; } = new List<RecipeIngredient>();
    
    /// <summary>
    /// Production orders for this product
    /// </summary>
    public virtual ICollection<ProductionOrder> ProductionOrders { get; set; } = new List<ProductionOrder>();
}
