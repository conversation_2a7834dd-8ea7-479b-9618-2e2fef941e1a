using System.ComponentModel.DataAnnotations;
using IFC.Domain.Enums;

namespace IFC.Domain.Entities;

/// <summary>
/// Represents a movement of inventory (in/out transactions)
/// </summary>
public class InventoryMovement
{
    [Key]
    public int MovementId { get; set; }
    
    /// <summary>
    /// Inventory record this movement affects
    /// </summary>
    public int InventoryId { get; set; }
    
    /// <summary>
    /// Product being moved
    /// </summary>
    public int ProductId { get; set; }
    
    /// <summary>
    /// Cost center where movement occurred
    /// </summary>
    public int CostCenterId { get; set; }
    
    /// <summary>
    /// Type of movement (receipt, issue, transfer, etc.)
    /// </summary>
    public InventoryMovementType MovementType { get; set; }
    
    /// <summary>
    /// Reference number for the transaction
    /// </summary>
    [StringLength(50)]
    public string ReferenceNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// Quantity moved (positive for receipts, negative for issues)
    /// </summary>
    public decimal Quantity { get; set; }
    
    /// <summary>
    /// Unit of measure for the quantity
    /// </summary>
    [StringLength(50)]
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Cost per unit for this movement
    /// </summary>
    public decimal UnitCost { get; set; }
    
    /// <summary>
    /// Total cost for this movement
    /// </summary>
    public decimal TotalCost => Math.Abs(Quantity) * UnitCost;
    
    /// <summary>
    /// Quantity on hand before this movement
    /// </summary>
    public decimal QuantityBefore { get; set; }
    
    /// <summary>
    /// Quantity on hand after this movement
    /// </summary>
    public decimal QuantityAfter { get; set; }
    
    /// <summary>
    /// Production order that caused this movement (if applicable)
    /// </summary>
    public int? ProductionOrderId { get; set; }
    
    /// <summary>
    /// Batch/lot number for expirable products
    /// </summary>
    public int? BatchId { get; set; }
    
    /// <summary>
    /// Date and time of the movement
    /// </summary>
    public DateTime MovementDate { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// User who performed the movement
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// Notes or comments about the movement
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Indicates if this movement has been posted/finalized
    /// </summary>
    public bool IsPosted { get; set; } = true;
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    
    /// <summary>
    /// Inventory record this movement affects
    /// </summary>
    public virtual Inventory Inventory { get; set; } = null!;
    
    /// <summary>
    /// Product being moved
    /// </summary>
    public virtual Product Product { get; set; } = null!;
    
    /// <summary>
    /// Production order that caused this movement (if applicable)
    /// </summary>
    public virtual ProductionOrder? ProductionOrder { get; set; }
    
    /// <summary>
    /// Batch/lot record for expirable products
    /// </summary>
    public virtual InventoryBatch? Batch { get; set; }
    
    // Calculated properties
    
    /// <summary>
    /// Indicates if this is an inbound movement (receipt)
    /// </summary>
    public bool IsInbound => Quantity > 0;
    
    /// <summary>
    /// Indicates if this is an outbound movement (issue)
    /// </summary>
    public bool IsOutbound => Quantity < 0;
    
    /// <summary>
    /// Absolute quantity (always positive)
    /// </summary>
    public decimal AbsoluteQuantity => Math.Abs(Quantity);
}
