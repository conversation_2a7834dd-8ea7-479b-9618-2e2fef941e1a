namespace IFC.Domain.Enums;

/// <summary>
/// Represents the type of production step
/// </summary>
public enum ProductionStepType
{
    /// <summary>
    /// Material preparation step
    /// </summary>
    MaterialPreparation = 1,
    
    /// <summary>
    /// Mixing or combining ingredients
    /// </summary>
    Mixing = 2,
    
    /// <summary>
    /// Cooking or heating process
    /// </summary>
    Cooking = 3,
    
    /// <summary>
    /// Cooling or chilling process
    /// </summary>
    Cooling = 4,
    
    /// <summary>
    /// Assembly or construction step
    /// </summary>
    Assembly = 5,
    
    /// <summary>
    /// Packaging step
    /// </summary>
    Packaging = 6,
    
    /// <summary>
    /// Quality control inspection
    /// </summary>
    QualityControl = 7,
    
    /// <summary>
    /// Waiting or resting period
    /// </summary>
    Waiting = 8,
    
    /// <summary>
    /// Sub-recipe production step
    /// </summary>
    SubRecipeProduction = 9,
    
    /// <summary>
    /// Material consumption/usage
    /// </summary>
    MaterialConsumption = 10,
    
    /// <summary>
    /// Final inspection and approval
    /// </summary>
    FinalInspection = 11,
    
    /// <summary>
    /// Custom or other type of step
    /// </summary>
    Other = 99
}

/// <summary>
/// Represents the status of a production step
/// </summary>
public enum ProductionStepStatus
{
    /// <summary>
    /// Step is planned but not yet started
    /// </summary>
    Pending = 1,
    
    /// <summary>
    /// Step is ready to start
    /// </summary>
    Ready = 2,
    
    /// <summary>
    /// Step is currently in progress
    /// </summary>
    InProgress = 3,
    
    /// <summary>
    /// Step is paused
    /// </summary>
    Paused = 4,
    
    /// <summary>
    /// Step has been completed
    /// </summary>
    Completed = 5,
    
    /// <summary>
    /// Step has been skipped
    /// </summary>
    Skipped = 6,
    
    /// <summary>
    /// Step failed and needs attention
    /// </summary>
    Failed = 7,
    
    /// <summary>
    /// Step is on hold
    /// </summary>
    OnHold = 8
}
