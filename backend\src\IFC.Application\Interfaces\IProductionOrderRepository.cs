using IFC.Domain.Entities;
using IFC.Domain.Enums;

namespace IFC.Application.Interfaces;

/// <summary>
/// Repository interface for ProductionOrder entity
/// </summary>
public interface IProductionOrderRepository
{
    /// <summary>
    /// Gets a production order by ID
    /// </summary>
    /// <param name="id">Production order ID</param>
    /// <returns>Production order or null if not found</returns>
    Task<ProductionOrder?> GetByIdAsync(int id);
    
    /// <summary>
    /// Gets a production order by ID with full hierarchy (parent and children)
    /// </summary>
    /// <param name="id">Production order ID</param>
    /// <returns>Production order with hierarchy or null if not found</returns>
    Task<ProductionOrder?> GetByIdWithHierarchyAsync(int id);
    
    /// <summary>
    /// Gets a production order by order number
    /// </summary>
    /// <param name="orderNumber">Order number</param>
    /// <returns>Production order or null if not found</returns>
    Task<ProductionOrder?> GetByOrderNumberAsync(string orderNumber);
    
    /// <summary>
    /// Gets production orders by status
    /// </summary>
    /// <param name="status">Status to filter by</param>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>List of production orders</returns>
    Task<IEnumerable<ProductionOrder>> GetByStatusAsync(ProductionOrderStatus status, int? costCenterId = null);
    
    /// <summary>
    /// Gets production orders by product
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="includeCompleted">Include completed orders</param>
    /// <returns>List of production orders</returns>
    Task<IEnumerable<ProductionOrder>> GetByProductIdAsync(int productId, bool includeCompleted = false);
    
    /// <summary>
    /// Gets production orders by date range
    /// </summary>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>List of production orders</returns>
    Task<IEnumerable<ProductionOrder>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, int? costCenterId = null);
    
    /// <summary>
    /// Gets child production orders for a parent order
    /// </summary>
    /// <param name="parentOrderId">Parent production order ID</param>
    /// <returns>List of child production orders</returns>
    Task<IEnumerable<ProductionOrder>> GetChildOrdersAsync(int parentOrderId);
    
    /// <summary>
    /// Gets top-level production orders (no parent)
    /// </summary>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>List of top-level production orders</returns>
    Task<IEnumerable<ProductionOrder>> GetTopLevelOrdersAsync(int? costCenterId = null);
    
    /// <summary>
    /// Gets production orders ready to start
    /// </summary>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>Production orders that can be started</returns>
    Task<IEnumerable<ProductionOrder>> GetReadyToStartAsync(int? costCenterId = null);
    
    /// <summary>
    /// Gets overdue production orders
    /// </summary>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>Production orders past their planned end date</returns>
    Task<IEnumerable<ProductionOrder>> GetOverdueOrdersAsync(int? costCenterId = null);
    
    /// <summary>
    /// Gets the last production order (for order number generation)
    /// </summary>
    /// <returns>Last production order or null</returns>
    Task<ProductionOrder?> GetLastOrderAsync();
    
    /// <summary>
    /// Gets production orders with material shortages
    /// </summary>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>Production orders with insufficient materials</returns>
    Task<IEnumerable<ProductionOrder>> GetOrdersWithMaterialShortagesAsync(int? costCenterId = null);
    
    /// <summary>
    /// Adds a new production order
    /// </summary>
    /// <param name="productionOrder">Production order to add</param>
    /// <returns>Added production order with ID</returns>
    Task<ProductionOrder> AddAsync(ProductionOrder productionOrder);
    
    /// <summary>
    /// Updates an existing production order
    /// </summary>
    /// <param name="productionOrder">Production order to update</param>
    /// <returns>Updated production order</returns>
    Task<ProductionOrder> UpdateAsync(ProductionOrder productionOrder);
    
    /// <summary>
    /// Updates production order status
    /// </summary>
    /// <param name="id">Production order ID</param>
    /// <param name="status">New status</param>
    /// <param name="userId">User making the change</param>
    /// <returns>True if update successful</returns>
    Task<bool> UpdateStatusAsync(int id, ProductionOrderStatus status, int userId);
    
    /// <summary>
    /// Deletes a production order (soft delete)
    /// </summary>
    /// <param name="id">Production order ID to delete</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteAsync(int id);
    
    /// <summary>
    /// Gets production statistics for a date range
    /// </summary>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>Production statistics</returns>
    Task<ProductionStatistics> GetProductionStatisticsAsync(DateTime fromDate, DateTime toDate, int? costCenterId = null);
}

/// <summary>
/// Production statistics model
/// </summary>
public class ProductionStatistics
{
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int InProgressOrders { get; set; }
    public int OverdueOrders { get; set; }
    public decimal TotalQuantityProduced { get; set; }
    public decimal TotalProductionCost { get; set; }
    public decimal AverageCompletionTime { get; set; }
    public decimal OnTimeDeliveryPercentage { get; set; }
}
