using IFC.Domain.Entities;
using IFC.Domain.Models;
using IFC.Domain.Enums;

namespace IFC.Application.Interfaces;

/// <summary>
/// Service interface for production operations
/// Enhanced version of VB.NET production logic with recursive processing
/// </summary>
public interface IProductionService
{
    /// <summary>
    /// Creates a new production order with recursive processing
    /// Replicates and enhances VB.NET production order creation
    /// </summary>
    Task<ProductionOrder> CreateProductionOrderAsync(int productId, decimal quantity, int costCenterId, DateTime? requestedDate = null, string? notes = null);

    /// <summary>
    /// Gets production order by ID
    /// </summary>
    Task<ProductionOrder?> GetProductionOrderAsync(int productionOrderId);

    /// <summary>
    /// Gets all production orders with optional filtering
    /// </summary>
    Task<IEnumerable<ProductionOrder>> GetProductionOrdersAsync(ProductionOrderStatus? status = null, int? costCenterId = null, DateTime? fromDate = null, DateTime? toDate = null);

    /// <summary>
    /// Validates production order before creation
    /// Enhanced version of VB.NET RowEffectQuantity validation
    /// </summary>
    Task<ProductionValidationResult> ValidateProductionOrderAsync(int productId, decimal quantity, int costCenterId, DateTime? requestedDate = null);

    /// <summary>
    /// Starts production order
    /// </summary>
    Task<ProductionOrder> StartProductionAsync(int productionOrderId, DateTime? actualStartDate = null, string? notes = null);

    /// <summary>
    /// Completes production order
    /// </summary>
    Task<ProductionOrder> CompleteProductionAsync(int productionOrderId, DateTime? actualEndDate = null, decimal? actualQuantityProduced = null, decimal? actualCost = null, string? notes = null);

    /// <summary>
    /// Cancels production order
    /// </summary>
    Task<ProductionOrder> CancelProductionAsync(int productionOrderId, string reason, string? notes = null);

    /// <summary>
    /// Updates production order status
    /// </summary>
    Task<ProductionOrder> UpdateProductionStatusAsync(int productionOrderId, ProductionOrderStatus status, string? notes = null);

    /// <summary>
    /// Gets production hierarchy for a product
    /// Shows the complete recursive structure that VB.NET cannot handle
    /// </summary>
    Task<ProductionHierarchy> GetProductionHierarchyAsync(int productId, decimal quantity);

    /// <summary>
    /// Gets material requirements for production
    /// Enhanced version of VB.NET material calculation
    /// </summary>
    Task<MaterialRequirements> GetMaterialRequirementsAsync(int productId, decimal quantity, int costCenterId);

    /// <summary>
    /// Reserves materials for production
    /// </summary>
    Task<MaterialReservationResult> ReserveMaterialsAsync(int productionOrderId);

    /// <summary>
    /// Releases reserved materials
    /// </summary>
    Task<MaterialReservationResult> ReleaseMaterialsAsync(int productionOrderId);

    /// <summary>
    /// Gets production schedule
    /// </summary>
    Task<IEnumerable<ProductionOrder>> GetProductionScheduleAsync(DateTime fromDate, DateTime toDate, int? costCenterId = null);

    /// <summary>
    /// Reschedules production order
    /// </summary>
    Task<ProductionOrder> RescheduleProductionAsync(int productionOrderId, DateTime newScheduledStartDate, DateTime newScheduledEndDate);

    /// <summary>
    /// Gets production statistics
    /// </summary>
    Task<object> GetProductionStatisticsAsync(DateTime fromDate, DateTime toDate, int? costCenterId = null);

    /// <summary>
    /// Checks if product can be produced
    /// </summary>
    Task<bool> CanProduceAsync(int productId, decimal quantity, int costCenterId);

    /// <summary>
    /// Gets estimated production cost
    /// </summary>
    Task<decimal> GetEstimatedProductionCostAsync(int productId, decimal quantity);

    /// <summary>
    /// Gets estimated production time
    /// </summary>
    Task<int> GetEstimatedProductionTimeAsync(int productId, decimal quantity);

    /// <summary>
    /// Updates production order status by status enum
    /// </summary>
    Task<ProductionOrder> UpdateProductionOrderStatusAsync(int productionOrderId, ProductionOrderStatus status);
}
