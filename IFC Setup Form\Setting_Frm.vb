﻿Imports C1.Win.C1FlexGrid

Public Class Setting_Frm
    Dim Conn As New Conn_Cls

    Dim ClsSetting As New Setting_Cls
    Dim IsLoading, IsEnd As Boolean
    Sub New(Optional IsClose As Boolean = False)

        ' This call is required by the designer.
        InitializeComponent()
        IsLoading = True
        ' Add any initialization after the InitializeComponent() call.
        Lbltitle.Text = "Setting InterFace SCM Sysytem With " & Conn.POS
        FillComp_Company()
        Clears()

        IsLoading = False
        IsEnd = IsClose
    End Sub
    Public Sub FillComp_Company()
        Comp_CompanySCM.DataSource = Nothing
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = ClsSetting.FillCompany

        Comp_CompanySCM.DataSource = Dt
        Comp_CompanySCM.ValueMember = Dt.Columns("Comp_Id").ToString()
        Comp_CompanySCM.DisplayMember = Dt.Columns("Comp_Name").ToString()
    End Sub
    Public Sub Clears()
        TxtBranch_Id.Text = 0
        TxtBranch_Name.Text = String.Empty
        TxtBrand_Id.Text = 0
        TxtBrand_Name.Text = String.Empty
        TxtCostCenter_Id.Text = 0
        TxtCostCenter_Name.Text = String.Empty
        TxtID.Text = String.Empty
        Comp_CompanySCM.Text = Nothing
        ' Comp_CompanySCM.SelectedValue = Nothing

        CompBranchSCM.Text = Nothing
        ' CompBranchSCM.SelectedValue = Nothing
        CompStoreSCM.Text = Nothing
        '  CompStoreSCM.SelectedValue = Nothing
        LoadFG()
        FillGrid()
    End Sub
    Public Sub FillGrid()
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = ClsSetting.Show_() 'ClsItemLink.Show_()
        DGVCost.Rows.Count = 1
        DGVCost.Cols.Count = 5

        DGVCost.Cols(0).Caption = "#"
        DGVCost.Cols(1).Visible = False
        DGVCost.Cols(0).Visible = False
        DGVCost.Cols(4).Visible = False
        DGVCost.Cols(2).Caption = "Cost Center Name"
        DGVCost.Cols(3).Caption = "Choose"

        Dim cs As CellStyle
        cs = DGVCost.Styles.Add("bool")
        cs.DataType = GetType(Boolean)
        cs.ImageAlign = ImageAlignEnum.CenterCenter
        ' rc = FG_PR.Cols(3).Style
        DGVCost.Cols(3).Style = DGVCost.Styles("bool")

        ' Dim cs As CellStyle
        cs = DGVCost.Styles.Add("bool")
        cs.DataType = GetType(Boolean)
        cs.ImageAlign = ImageAlignEnum.CenterCenter
        ' rc = FG_PR.Cols(3).Style
        DGVCost.Cols(4).Style = DGVCost.Styles("bool")

        Dim Ro As Integer
        Ro = 0 ' DGVCost.Rows.Count
        For I As Integer = 0 To Dt.Rows.Count - 1
            Ro += 1
            DGVCost.Rows.Count += 1
            DGVCost(Ro, 1) = Dt.Rows(I)(0)
            DGVCost(Ro, 2) = Dt.Rows(I)(1)
        Next

        DGVCost.Cols(0).AllowEditing = False
        DGVCost.Cols(1).AllowEditing = False
        DGVCost.Cols(2).AllowEditing = False

        ' DGVCost.Cols(0).Width = Val(DGVCost.Width) * Val(0.2)
        DGVCost.Cols(2).Width = Val(DGVCost.Width) * Val(0.75)
        DGVCost.Cols(3).Width = Val(DGVCost.Width) * Val(0.2)
        GeatDataDGV()
    End Sub

    Public Sub GeatDataDGV()
        Dim Dt As New DataTable
        Dt.Clear()
        If String.IsNullOrWhiteSpace(TxtID.Text) Then Return
        ' Dt = ClsItemLink.FindByItemId(TxtID.Text)
        Dt = ClsSetting.GetCostDataLink(Convert.ToInt32(TxtID.Text))
        For T As Integer = 0 To Dt.Rows.Count - 1

            For G As Integer = 0 To DGVCost.Rows.Count - 1
                '   DGVCost(G, 4) = ClsItemLink.HasTransAction(Val(TxtProductId.Text), Val(DGVCost(G, 1)))

                'If DGVCost(G, 4).ToString() = "True" Then
                '    DGVCost.Rows(G).AllowEditing = False
                'End If
                If Dt.Rows(T)("CostCenter_Id") = DGVCost(G, 1) Then
                    DGVCost(G, 3) = "True"


                End If

            Next
        Next
    End Sub
    Public Sub LoadFG()
        FG.DataSource = Nothing
        FG.DataSource = ClsSetting.LoadFGData

        FG.Cols.Fixed = 0
        'FG.Cols("Company_IdSCM").Visible = False
        'FG.Cols("Branch_IdSCM").Visible = False
        'FG.Cols("Store_IdSCM").Visible = False
        FG.Cols("Company_IdPOS").Visible = False
        FG.Cols("Brand_IdPOS").Visible = False
        FG.Cols("CostCenter_IdPOS").Visible = False
        FG.Cols("IsDelete").Visible = False

        FG.Cols("Ser").Caption = "#"
        'FG.Cols("Company_NameSCM").Caption = "Company NameSCM"
        'FG.Cols("Branch_NameSCM").Caption = "Branch NameSCM"
        'FG.Cols("Store_NameSCM").Caption = "Store NameSCM"
        FG.Cols("Company_NamePOS").Caption = "Company NamePOS"
        FG.Cols("Brand_NamePOS").Caption = "Brand NamePOS"
        FG.Cols("CostCenter_NamePOS").Caption = "CostCenter NamePOS"

        FG.Cols("Ser").Width = FG.Width * Val(0.1)
        'FG.Cols("Company_NameSCM").Width = FG.Width * Val(0.15)
        'FG.Cols("Branch_NameSCM").Width = FG.Width * Val(0.15)
        'FG.Cols("Store_NameSCM").Width = FG.Width * Val(0.15)
        FG.Cols("Company_NamePOS").Width = FG.Width * Val(0.3)
        FG.Cols("Brand_NamePOS").Width = FG.Width * Val(0.3)
        FG.Cols("CostCenter_NamePOS").Width = FG.Width * Val(0.3)
    End Sub

    Private Sub TxtBrand_Id_Enter(sender As Object, e As EventArgs) Handles TxtBrand_Id.Enter, TxtBranch_Id.Enter, TxtCostCenter_Id.Enter
        TxtEnter_Number(sender, e)
    End Sub

    Private Sub TxtBrand_Id_KeyPress(sender As Object, e As KeyPressEventArgs) Handles TxtBrand_Id.KeyPress, TxtBranch_Id.KeyPress, TxtCostCenter_Id.KeyPress
        TxtKeyPress_Number(sender, e)
    End Sub

    Private Sub TxtBrand_Id_Leave(sender As Object, e As EventArgs) Handles TxtBrand_Id.Leave, TxtBranch_Id.Leave, TxtCostCenter_Id.Leave
        TxtLeav_Number(sender, e)
    End Sub

    Public Function ValidationControl(Saved As Boolean, Optional IsDelet As Boolean = False) As Boolean
        Dim FillOrChoose As String = ""
        Dim HASError As Boolean
        If Saved Then
            FillOrChoose = " Enter "
        Else
            FillOrChoose = " Choose "
        End If
        If Saved = False Then
            If TxtID.Text = String.Empty Then
                HASError = True
                '     Comp_CompanySCM.Focus()
                GoTo E
            End If
        End If
        If IsDelet = False Then
            'If Comp_CompanySCM.SelectedIndex = -1 Then
            '    HASError = True
            '    Comp_CompanySCM.Focus()
            '    GoTo E
            'End If

            'If CompBranchSCM.SelectedIndex = -1 Then
            '    HASError = True
            '    CompBranchSCM.Focus()
            '    GoTo E
            'End If

            'If CompStoreSCM.SelectedIndex = -1 Then
            '    HASError = True
            '    CompStoreSCM.Focus()
            '    GoTo E
            'End If

            If TxtBranch_Id.Text = "0" Then
                HASError = True
                TxtBranch_Id.Focus()
                GoTo E
            End If

            If TxtBranch_Name.Text = String.Empty Then
                HASError = True
                TxtBranch_Name.Focus()
                GoTo E
            End If

            If TxtBrand_Id.Text = "0" Then
                HASError = True
                TxtBrand_Id.Focus()
                GoTo E
            End If

            If TxtBrand_Name.Text = String.Empty Then
                HASError = True
                TxtBrand_Name.Focus()
                GoTo E
            End If

            If TxtCostCenter_Id.Text = "0" Then
                HASError = True
                TxtCostCenter_Id.Focus()
                GoTo E
            End If

            If TxtCostCenter_Name.Text = String.Empty Then
                HASError = True
                TxtCostCenter_Name.Focus()
                GoTo E
            End If
        End If
E:
        If HASError Then MessageBox.Show("Please " & FillOrChoose & " Data", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)

        Return HASError
    End Function

    Private Sub Btn_New_Click(sender As Object, e As EventArgs) Handles Btn_New.Click
        Try
            Clears()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Btn_Save_Click(sender As Object, e As EventArgs) Handles Btn_Save.Click
        Try
            Dim Okays As Boolean
            Okays = ValidationControl(True)
            Dim Ids As Integer = 0
            If Okays Then Return
            'ClsSetting.SaveEditSetting(Val(TxtID.Text), Val(Comp_CompanySCM.SelectedValue), Comp_CompanySCM.Text, Val(CompBranchSCM.SelectedValue), CompBranchSCM.Text,
            '                           Val(CompStoreSCM.SelectedValue), CompStoreSCM.Text, Val(TxtBranch_Id.Text), TxtBranch_Name.Text, Val(TxtBrand_Id.Text), TxtBrand_Name.Text,
            '                           Val(TxtCostCenter_Id.Text), TxtCostCenter_Name.Text, True)

            Ids = ClsSetting.SaveEditSetting(Val(TxtID.Text),
                                    Val(TxtBrand_Id.Text), TxtBrand_Name.Text, Val(TxtBranch_Id.Text), TxtBranch_Name.Text,
                                   Val(TxtCostCenter_Id.Text), TxtCostCenter_Name.Text, True)


            SaveCostLink(Ids)

            Clears()
        Catch ex As Exception

        End Try
    End Sub
    Public Sub SaveCostLink(Ids As Integer)
        ClsSetting.DeleteCostLink(Ids)


        For R As Integer = 0 To DGVCost.Rows.Count - 1
            If DGVCost(R, 3) = "True" Then
                ClsSetting.SaveCostData(Ids, Convert.ToInt32(DGVCost(R, 1)), DGVCost(R, 2))
            End If
        Next
    End Sub
    Private Sub Btn_Edit_Click(sender As Object, e As EventArgs) Handles Btn_Edit.Click
        Try
            Dim Okays As Boolean
            Okays = ValidationControl(False)
            If Okays Then Return
            Dim Ids As Integer = 0
            Ids = ClsSetting.SaveEditSetting(Val(TxtID.Text),
                                    Val(TxtBrand_Id.Text), TxtBrand_Name.Text, Val(TxtBranch_Id.Text), TxtBranch_Name.Text,
                                      Val(TxtCostCenter_Id.Text), TxtCostCenter_Name.Text)

            SaveCostLink(Ids)
            Clears()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub TxtID_TextChanged(sender As Object, e As EventArgs) Handles TxtID.TextChanged
        If Val(TxtID.Text) > 0 Then
            Btn_Edit.Enabled = True
            Btn_Save.Enabled = False
            Btn_Delete.Enabled = True
        Else
            Btn_Edit.Enabled = False
            Btn_Save.Enabled = True
            Btn_Delete.Enabled = False
        End If
    End Sub

    Private Sub Comp_CompanySCM_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Comp_CompanySCM.SelectedIndexChanged
        If IsLoading Then Return
        If Comp_CompanySCM.SelectedIndex = -1 Then
            CompBranchSCM.DataSource = Nothing

        Else

            FillComp_Branch(Val(Comp_CompanySCM.SelectedValue))
        End If
    End Sub

    Public Sub FillComp_Branch(CompanyId As Integer)
        IsLoading = True
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = ClsSetting.FillBranch(CompanyId)
        CompBranchSCM.DataSource = Nothing
        CompBranchSCM.DataSource = Dt
        CompBranchSCM.ValueMember = Dt.Columns("Location_Id").ToString()
        CompBranchSCM.DisplayMember = Dt.Columns("Location_Name").ToString()
        CompBranchSCM.Text = Nothing
        IsLoading = False
    End Sub

    Private Sub CompBranchSCM_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CompBranchSCM.SelectedIndexChanged
        If IsLoading Then Return
        If CompBranchSCM.SelectedIndex = -1 Then
            CompStoreSCM.DataSource = Nothing

        Else

            FillComp_Store(Val(CompBranchSCM.SelectedValue))
        End If
    End Sub

    Public Sub FillComp_Store(branchId As Integer)
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = ClsSetting.FillStore(branchId)

        CompStoreSCM.DataSource = Dt
        CompStoreSCM.ValueMember = Dt.Columns("Store_id").ToString()
        CompStoreSCM.DisplayMember = Dt.Columns("Store_Name").ToString()
        CompStoreSCM.Text = Nothing
    End Sub

    Private Sub FG_DoubleClick(sender As Object, e As EventArgs) Handles FG.DoubleClick
        Dim RowF As Integer = FG.RowSel
        If RowF = 0 Then Return
        'Comp_CompanySCM.SelectedValue = FG(RowF, "Company_IdSCM")
        'Comp_CompanySCM.Text = FG(RowF, "Company_NameSCM")
        'CompBranchSCM.SelectedValue = FG(RowF, "Branch_IdSCM")
        'CompBranchSCM.Text = FG(RowF, "Branch_NameSCM")
        'CompStoreSCM.SelectedValue = FG(RowF, "Store_IdSCM")
        'CompStoreSCM.Text = FG(RowF, "Store_NameSCM")
        TxtBrand_Id.Text = FG(RowF, "Company_IdPOS")
        TxtBrand_Name.Text = FG(RowF, "Company_NamePOS")
        TxtBranch_Id.Text = FG(RowF, "Brand_IdPOS")
        TxtBranch_Name.Text = FG(RowF, "Brand_NamePOS")
        TxtCostCenter_Id.Text = FG(RowF, "CostCenter_IdPOS")
        TxtCostCenter_Name.Text = FG(RowF, "CostCenter_NamePOS")
        TxtID.Text = FG(RowF, "Ser")

        FillGrid()
    End Sub

    Private Sub Setting_Frm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If IsEnd Then If IsEnd Then Process.GetCurrentProcess.Kill()
    End Sub

    Private Sub Setting_Frm_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub Btn_Delete_Click(sender As Object, e As EventArgs) Handles Btn_Delete.Click
        Try

            Dim result As DialogResult = MsgBox("Are You Want To Delete ", 1 + 32 + 0 + 0, "Delete Confirmation")

            If result = System.Windows.Forms.DialogResult.OK Then
                Dim Okays As Boolean
                Okays = ValidationControl(False, True)
                If Okays Then Return

                ClsSetting.DeleteSetting(Val(TxtID.Text))
                Clears()
            End If
        Catch ex As Exception

        End Try
    End Sub


End Class