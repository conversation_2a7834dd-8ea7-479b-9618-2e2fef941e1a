namespace IFC.Domain.Models;

/// <summary>
/// Result of production order validation
/// </summary>
public class ProductionValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<MaterialAvailabilityIssue> MaterialIssues { get; set; } = new();
    public List<DependencyIssue> DependencyIssues { get; set; } = new();
    public decimal EstimatedCost { get; set; }
    public int EstimatedDurationMinutes { get; set; }
    public DateTime EarliestStartDate { get; set; }
    public DateTime EstimatedCompletionDate { get; set; }
}

/// <summary>
/// Material availability issue
/// </summary>
public class MaterialAvailabilityIssue
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal RequiredQuantity { get; set; }
    public decimal AvailableQuantity { get; set; }
    public decimal ShortageQuantity => RequiredQuantity - AvailableQuantity;
    public string Unit { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty; // "Shortage", "Expired", "OnHold", etc.
    public string? Suggestion { get; set; }
}

/// <summary>
/// Dependency issue
/// </summary>
public class DependencyIssue
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty; // "CircularDependency", "MissingRecipe", etc.
    public string Description { get; set; } = string.Empty;
    public List<int> DependencyChain { get; set; } = new();
}

/// <summary>
/// Production hierarchy representation
/// </summary>
public class ProductionHierarchy
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int HierarchyLevel { get; set; }
    public bool IsRecipe { get; set; }
    public bool IsProduction { get; set; }
    public decimal EstimatedCost { get; set; }
    public int EstimatedDurationMinutes { get; set; }
    public List<ProductionHierarchy> Children { get; set; } = new();
    public List<MaterialRequirement> DirectMaterials { get; set; } = new();
}

/// <summary>
/// Material requirements breakdown
/// </summary>
public class MaterialRequirements
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public List<MaterialRequirement> Requirements { get; set; } = new();
    public decimal TotalEstimatedCost => Requirements.Sum(r => r.TotalCost);
}

/// <summary>
/// Individual material requirement
/// </summary>
public class MaterialRequirement
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal RequiredQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal UnitCost { get; set; }
    public decimal TotalCost => RequiredQuantity * UnitCost;
    public int HierarchyLevel { get; set; }
    public bool IsRawMaterial { get; set; }
    public bool RequiresProduction { get; set; }
    public int? ParentProductId { get; set; }
    public string? ParentProductCode { get; set; }
}

/// <summary>
/// Material reservation result
/// </summary>
public class MaterialReservationResult
{
    public bool IsSuccessful { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<MaterialReservation> Reservations { get; set; } = new();
    public decimal TotalReservedValue { get; set; }
}

/// <summary>
/// Individual material reservation
/// </summary>
public class MaterialReservation
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public decimal ReservedQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public DateTime ReservationDate { get; set; }
    public decimal UnitCost { get; set; }
    public decimal TotalValue => ReservedQuantity * UnitCost;
}

/// <summary>
/// Production plan with sequenced steps
/// </summary>
public class ProductionPlan
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public List<ProductionPlanStep> Steps { get; set; } = new();
    public int TotalDurationMinutes => Steps.Sum(s => s.DurationMinutes);
    public decimal TotalCost => Steps.Sum(s => s.EstimatedCost);
}

/// <summary>
/// Individual step in production plan
/// </summary>
public class ProductionPlanStep
{
    public int StepNumber { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string StepType { get; set; } = string.Empty; // "Production", "SubRecipe", "Material"
    public int DurationMinutes { get; set; }
    public decimal EstimatedCost { get; set; }
    public List<int> Dependencies { get; set; } = new(); // Step numbers this step depends on
    public int HierarchyLevel { get; set; }
}

/// <summary>
/// Dependency validation result
/// </summary>
public class DependencyValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<DependencyIssue> Issues { get; set; } = new();
    public bool HasCircularDependency { get; set; }
    public List<int> CircularDependencyChain { get; set; } = new();
    public int MaxDepth { get; set; }
}

/// <summary>
/// Production sequence step
/// </summary>
public class ProductionSequenceStep
{
    public int SequenceNumber { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int HierarchyLevel { get; set; }
    public DateTime PlannedStartDate { get; set; }
    public DateTime PlannedEndDate { get; set; }
    public List<int> Prerequisites { get; set; } = new(); // Sequence numbers that must complete first
    public bool CanRunInParallel { get; set; }
}
