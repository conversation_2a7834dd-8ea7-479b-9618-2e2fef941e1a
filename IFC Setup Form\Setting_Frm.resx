﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAEBAAAAEAIABoBAAAJgAAACAgAAABACAAqBAAAI4EAAAoAAAAEAAAACAAAAABACAAAAAAAAAE
        AAASCwAAEgsAAAAAAAAAAAAAAAAAAAAAAAAAAAAjAAAAMwAAADMAAAAiAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAIgAAADMAAAAzAAAAI7qGHQC6hh0AqXkZwMGLJv+/iiP/qHkYvQAAACF1eYUAdXmFAMGI
        FgC8hx0AAAAAIqd4Gb3AiyL/wYwl/6l5GcDFkjAAxJEvAMKOKv/uu2j/6K9P/9GXLf+qeBK5AAAAIXF4
        igDAiBgAAAAAI6Z4GbnQlSv/56tJ/+24Yv/Cjin/xZQyAMSTMQDBjir///LS/+m1Xv/nrEv/1pgu/6p3
        ErkAAAAhvYcaAK9+GbjTmC3/5qlG/+izWf//9tP/wY4q/7uGHAC6hRsAt4IXrM+eQv//78z/7sR3/+as
        S//VmC//p3cWtQAAAC6ndQ+t6apD/+myV///887/1aZU/7eCF6zAhhQAvoYWALyFGAC3gROn26xW///u
        y//otF3/561L/9aYLf+cdyvtoai46dm3f///9Mv/3KxV/7aBFKi7hRsAio2WAIaKmADChg8AvoUTALeA
        EKbgtWn//+7L/+q0XP/rrUT/nHkw/93h6f+mqbL72q5Um7mAD6u+hRQAwIcVAI6MjAAAAAAiAAAAMwAA
        ACLAhQ4AuIEOot2sVP//8Mf/372D/7O1vP9+fn//cXJ2pwAAADMAAAAzAAAAJn2AiAAAAAAifnt6vYaE
        hP93eHu9AAAABgAAAB+cdSPsoXov/+Lm7f/Jycv/p6Wk/3JxcP+Ki4//e3yB/3BwcssAAAAvf317vaWh
        n/+/vbv/r66t/zU0NUxZWl+wo6Wq/+Di5/+CgID/0dDQ/8nHxv/T0dH/6+vs/+/u8P/c3Nz/gH588IqJ
        h//g3t7/ube1/8PBv/+fnJv/dHNx/+fn5/+tqqj/g4F//9vZ2P/OzMz/zcvJ/8vJyv+JhoT/h4WC/4qI
        hv+MiYewop+c/+Pj4v/Ewr//1NLR/7u5uP95d3X/g4B+kYuJh//h4eD/ysjH/8zKyf+OjIn/jYqIAIiG
        hQCKiIYAjYqIAKajoQCQjYv/6unq/9DOzf/U0tL/e3l3n4aDgQCTkY7/4+Lh/7u5uP+8urr/lJKP/wAA
        ADMAAAAzAAAAM4+MigCIhYMAhYKAqq6sqf/y8fH/rq2r/wAAAC0AAAAInJqX9NXU0//d29v/tLGv/6mm
        pP+dm5j/npyY/5ybl/+PjIoAioeFAImGhACCfnyotbOy///+/v+GhYPmOzo5U29ua1Kvrav/29rZ//Du
        7//s6+r/4uDg/7+8vP+joZ7/j4yKAIuIhgCLiIYAhoOBAH98eYSysK//trSz/4qIh/93dXOxqaekK6Wj
        oOSurKn/uba1/6uppf+npaLSqKajI8PwAADB4AAAwMAAAMBAAADAAAAA4AEAAPADAACIAQAAAAAAAAAA
        AAAAAAAAAAcAAMEAAADAAAAA4AAAAPAAAAAoAAAAIAAAAEAAAAABACAAAAAAAAAQAAASCwAAEgsAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAPAAAAFQAAABYAAAAUAAAADAAAAAMAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAA
        AA0AAAASAAAADQAAAAQAAAAAAAAAAAAAAAAAAAAFAAAAGAAAADMAAABBAAAAQwAAAD4AAAAtAAAAFAAA
        AAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAMAAAAUAAAALgAAADwAAAAuAAAAFAAAAAMAAAAAAAAAAAAAAA8AAAAzrXsY4beDGf+3hBv/uIUc/5tv
        GbYAAAAyAAAAFAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAADAAAAFAAAADKidBnFvokf/6J0GcUAAAAyAAAAFAAAAAQAAAAAAAAAFa57Ft/qy43/67JW/+ir
        Sv/boDv/uYYe/6B0GcIAAAAyAAAAFAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAwAAABQAAAAyn3MZwciPJf/npj7/yZMs/59yF8EAAAAuAAAADQAAAAAAAAAWwI4q///h
        sP/lrE7/5atM/+WqSf/eoj3/uYYd/6BzGcEAAAAwAAAAEQAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAMAAAAUAAAAMp9zGcHHjiX/5KQ8/+WqR//oslf/zJo6/6V2F8AAAAASAAAAAAAA
        ABTKm0L//92u/+WuUv/lrVD/5KtM/+WqSf/eoj3/uYYd/6F0GcEAAAApAAAADQAAAAIAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAADAAAAFAAAADKfcxnBx44l/+SkO//kqUb/5a5R/+6+cf//7Mv/toIW/wAA
        AA0AAAAAAAAADLqGHf//58T/7Lpr/+WvVP/lrlH/5KtM/+WqSf/eoj3/uYYe/3pYFIQAAAAnAAAADQAA
        AAIAAAAAAAAAAAAAAAAAAAAAAAAAAwAAABQAAAAyn3MZwceOJf/kpDv/5KlG/+StUf/tvW7//+fE/9Wn
        Vv+xfhezAAAABAAAAAAAAAADrnsVn+S6eP//5cD/7Lpr/+WvVP/lrlH/5atM/+WqSf/Ynjj/t4Qb/3tZ
        FIUAAAAmAAAACgAAAAEAAAAAAAAAAAAAAAQAAAAUAAAAMp9zGcHHjiX/5KQ7/+SpRv/krVH/7b1u///n
        w//UplT/r3wUrgAAAAMAAAAAAAAAAAAAAAAAAAAErnoRr+nBgv//5cD/7Lpr/+WvVP/lrlH/5atM/+aq
        Sf/UnDX/t4Qb/31aFIQAAAAhAAAACAAAAAEAAAAAAAAADQAAAC6fcxjBx44k/+SjO//kqUb/5K1R/+29
        bv//58P/1KZU/698FK4AAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADrnoRrunBgv//5cD/7Ltr/+Wv
        VP/lrlH/5atM/+aqSf/UnDX/t4Qb/zwrClQAAAAfAAAACAAAAAQAAAAcpXUUwceMH//koTj/46hF/+St
        Uf/tvW7//+fD/9SmVP+vfBSuAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEr3sRruO5
        d///5sL/7Lts/+WvVP/lrlH/5atM/+aqSf/UnDX/t4Qb/z8tClYAAAAiAAAAGgAAADPHkib///LK/+my
        WP/jrE3/7bxt///nw//UplT/r3wUrgAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAACpXQRYNSlU///58P/7bts/+WvVP/lrlH/5atM/+aqSf/UnDX/uIQa/zUlBlcAAAA+V1lgvrSQ
        R//Vpkz//+nA//LGfv//5sL/1KZT/698FK4AAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAACpXUSYdSmVP//58T/7btt/+WvVf/lrlH/5atM/+aqSf/VnDT/vIUU/1ZW
        Vrmkpqz/vL7G/7WQSP/So0n//+zK/9WmVP+vfBSuAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACqHcTYM6fSf//4Lf/7rxu/+WwVf/lrlH/5atM/+aq
        Sf/WmzH/t4kt/7y7vv+6vMH/6u/4/8ShWf/CjST/tH8VrwAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAA0AAAATAAAADQAAAAQAAAABkmcSIsWTNP//4Lj/7rxu/+Ww
        Vf/lrlH/5atM/+aqSP/VmS3/s4Up/+fp6//b3uP/goWRqwAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAUAAAALgAAADwAAAArAAAADQAAAAAAAAABlGkSJMaU
        NP//4bn/7r1v/+awVf/lrlH/5atM/+aqR//VmSz/xpY2/4GCibAAAAAIAAAAAAAAAAAAAAABAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAAFAAAADJ5dnTFioeF/3p5eMUAAAAdAAAABwAA
        AAQAAAAHXkIJMMKNJP/41J7/771u/+awVf/lrlH/5q1N/+epQ/+zfxj/AAAAOgAAAB4AAAAVAAAAFwAA
        ABgAAAAXAAAAFAAAAA0AAAAEAAAAAAAAAAAAAAAAAAAABAAAABQAAAAyd3VzwZuYlv+3tLL/iIWD/wAA
        ADcAAAAjAAAAHgAAACMAAAA1VFRWtrmKL//40pv/771v/+exV//psFL/yI0e/6SQav9kZGjpAAAAQwAA
        AEAAAABCGBgYTgAAAEMAAAA/AAAAMAAAABkAAAAHAAAAAQAAAAAAAAANAAAALnd1c8GgnJv/wsG+/7y7
        uf+al5X/cW9uvTk5OGk+PT1qODg5aVVWWr6kpar/u7m6/7aILP/5053/7bpn/9ScMv+7l1D/ubvC/6Wk
        pP9raWfkenh29np4df+EgoD/d3Vz/3d1c/9oZ2XFAAAAPAAAAB0AAAAGAAAAAAAAABN7enjArauq/9bU
        0//Ix8b/n5yb/5OQj/+ZlpT/goF//4OBf/+CgH7/kI6N/8G/vv+7u73/5ufn/8KSM/+1fxL8taR///Dz
        +//FxMX/ubi2/66sqv++vLv/1NLS/9PR0f/U0tH/z83M/6elo/9+fHr/NDMyUAAAABAAAAAAAAAADYqH
        hf///////////8C+vf+enJv/np2c/7+9u/+3tbP/sa6t/7Ctq/+QjIr/kpCO//Hw8P/c3uD/g4aQqwAA
        AAd5eoOrxsXI/+/v7//Dwb//t7Wz/8TCwP/IxsT/zszK/9XT0v/d29r/4ODf/8jGxv93dXPdAAAAEAAA
        AAAAAAAEiIaEtJ6bmf+7ubj/3dzc/7+9vP/Lycj/xMK//727uf+3s7H/sK2r/62qqP+MiIb/oJ6d/4WD
        g64AAAADAAAAAAAAABNzcXDCy8nI/9za2f+4trT/vLq4/8PBwP+sqqj/d3Vz/3l3df95d3X/enh2/3x6
        eP8AAAAGAAAAAAAAAAAAAAAAAAAAA3x6eKyzsbD/5+bl/9DOzf/KyMb/w8HA/727uf+2tLL/r6yq/7+9
        u/96eHb/AAAAFQAAAAAAAAAAAAAAFn18e+bJyMj/vbq5/7Kwrv+4trT/wL+8/46Liv8AAAAWAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADYN/ff//////3t7d/9DQz//Lycj/xcLC/727
        uf/EwsH//////3t5d/8AAAANAAAAAAAAAAAAAAAWg4B+/+vp6f+opaP/rqup/7Szsf+9u7n/kI2L/wAA
        ABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEgX58srKwr///////397d/9PQ
        z//Mysn/0M/N//////+uraz/eHZ0swAAAAQAAAAAAAAAAAAAABaEgX//5+bl/6ilov+rqKb/sK2r/7i2
        tP+PjYv/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADfnt5rrGv
        rv//////4eDf/9PS0f/Z2Nf/rKup/2lnZrMAAAAcAAAABQAAAAAAAAAAAAAAEYWCgP/08/P/p6Si/6qn
        pf+tqqj/s7Cu/4+Ni/8AAAAnAAAAFgAAABYAAAAWAAAAEQAAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAADfXt5rrCtrP//////4+Hg/9jX1f+amJb/dnRy5AAAADoAAAAcAAAACgAAAAQAAAAKhoOB6N/f
        3f+/vLr/qaak/6yopv+vrKr/j4yK/wAAAEgAAABDAAAAQwAAAEMAAAAyAAAAEQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAADfHp4rq6uq///////7ezs/9zb2v+dm5n/dnRy6wAAAEAAAAArAAAAHQAA
        ABF4dXNoop6e/+rq6f+tqqf/qaak/6ypp/+em5n/gYB+/4F/fP9/fXv/gH17/4KAfv8AAAARAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADe3l3rq+trP/8/Pz//v79//Py8/+0srH/d3Vz/0xK
        SYc/Pj1qAAAAMAAAABqIhYPnr6yr/+vq6f+/vLr/p6Si/6mmpP+rqKb/wb+8/+vq6f/j4+L/gX99/wAA
        AAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADfXt5moKAfv/R0M///f3+////
        ///l5eT/rq2s/5WTkf9vbWu/AAAAHwAAAA2JhoTnop6e/9/f3f/z8/L/8vLx//Pz8v/s7Ov/ycfG/4iF
        g/9ramgnAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYF9cD3l3
        dZmBf333mZeV/7Oysf++vbz/wcC//56dm/93dXPBAAAABAAAAAKFgoB1h4SC5YSBf/+ZlpT/gn99/4KA
        fv9/fXytd3VzJgAAAAEAAAAAAAAAAP/////Af/+DgD//AYAf/gCAD/wAgAf4AIAD8ACAAeAAgADAAcAA
        QAPgAAAH8AAAD/gAAB/8AAA//gAAf+AAAf/AgAN/gAAABwAAAAEAAAABAAAAAQAAAAEAAIABwAGAP+AB
        gD/gAYA/8AGAAfgAAAH8AAAB/gAAAf8AAAH/wAAD
</value>
  </data>
</root>