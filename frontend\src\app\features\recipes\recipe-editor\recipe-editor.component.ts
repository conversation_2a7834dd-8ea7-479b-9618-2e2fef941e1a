import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTreeModule } from '@angular/material/tree';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NestedTreeControl } from '@angular/cdk/tree';
import { MatTreeNestedDataSource } from '@angular/material/tree';

import { Recipe, RecipeIngredient, Product } from '../../../core/models/product.model';
import { RecipeService } from '../../../core/services/recipe.service';
import { ProductService } from '../../../core/services/product.service';
import { RecipeTreeNode } from '../../../core/models/recipe-tree.model';

@Component({
  selector: 'app-recipe-editor',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatTreeModule,
    MatExpansionModule,
    MatSnackBarModule,
    MatDialogModule,
    MatCheckboxModule,
    MatTooltipModule
  ],
  template: `
    <div class="recipe-editor-container">
      <mat-card class="recipe-header-card">
        <mat-card-header>
          <mat-card-title>
            {{ isEditMode ? 'Edit Recipe' : 'Create New Recipe' }}
          </mat-card-title>
          <mat-card-subtitle>
            {{ isEditMode ? recipe?.recipeName : 'Design a new recipe with nested ingredients' }}
          </mat-card-subtitle>
        </mat-card-header>
      </mat-card>

      <div class="editor-layout">
        <!-- Recipe Basic Information -->
        <mat-card class="recipe-form-card">
          <mat-card-header>
            <mat-card-title>Recipe Information</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <form [formGroup]="recipeForm" class="recipe-form">
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Product</mat-label>
                  <mat-select formControlName="productId" required>
                    <mat-option *ngFor="let product of availableProducts" [value]="product.productId">
                      {{ product.productCode }} - {{ product.productName }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="recipeForm.get('productId')?.hasError('required')">
                    Product is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Recipe Name</mat-label>
                  <input matInput formControlName="recipeName" required>
                  <mat-error *ngIf="recipeForm.get('recipeName')?.hasError('required')">
                    Recipe name is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Output Quantity</mat-label>
                  <input matInput type="number" formControlName="outputQuantity" required min="0.01" step="0.01">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Output Unit</mat-label>
                  <input matInput formControlName="outputUnit" required>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Production Time (minutes)</mat-label>
                  <input matInput type="number" formControlName="estimatedProductionTimeMinutes" required min="1">
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Labor Cost</mat-label>
                  <input matInput type="number" formControlName="laborCost" min="0" step="0.01">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Overhead Cost</mat-label>
                  <input matInput type="number" formControlName="overheadCost" min="0" step="0.01">
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Description</mat-label>
                <textarea matInput formControlName="description" rows="3"></textarea>
              </mat-form-field>
            </form>
          </mat-card-content>
        </mat-card>

        <!-- Recipe Tree Visualization -->
        <mat-card class="recipe-tree-card">
          <mat-card-header>
            <mat-card-title>Recipe Structure</mat-card-title>
            <mat-card-subtitle>
              Hierarchical view of recipe ingredients and nested recipes
            </mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="tree-actions">
              <button mat-raised-button color="primary" (click)="addIngredient()">
                <mat-icon>add</mat-icon>
                Add Ingredient
              </button>
              <button mat-raised-button color="accent" (click)="validateRecipe()" [disabled]="!recipeForm.valid">
                <mat-icon>check_circle</mat-icon>
                Validate Recipe
              </button>
            </div>

            <!-- Recipe Tree -->
            <mat-tree [dataSource]="treeDataSource" [treeControl]="treeControl" class="recipe-tree">
              <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
                <div class="tree-node" [class.nested-recipe]="node.isNestedRecipe" [class.raw-material]="!node.isNestedRecipe">
                  <mat-icon class="node-icon" [color]="node.isNestedRecipe ? 'accent' : 'primary'">
                    {{ node.isNestedRecipe ? 'restaurant' : 'inventory' }}
                  </mat-icon>
                  <span class="node-name">{{ node.name }}</span>
                  <span class="node-quantity">{{ node.quantity }} {{ node.unit }}</span>
                  <span class="node-cost">\${{ node.cost.toFixed(2) }}</span>
                  
                  <div class="node-actions">
                    <button mat-icon-button (click)="editIngredient(node)" matTooltip="Edit">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="removeIngredient(node)" matTooltip="Remove">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </mat-tree-node>

              <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
                <div class="tree-node nested-recipe">
                  <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
                    <mat-icon class="mat-icon-rtl-mirror">
                      {{ treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
                    </mat-icon>
                  </button>
                  <mat-icon class="node-icon" color="accent">restaurant</mat-icon>
                  <span class="node-name">{{ node.name }}</span>
                  <span class="node-quantity">{{ node.quantity }} {{ node.unit }}</span>
                  <span class="node-cost">\${{ node.cost.toFixed(2) }}</span>
                  <span class="nested-indicator" matTooltip="This ingredient is a nested recipe">
                    <mat-icon>account_tree</mat-icon>
                  </span>
                  
                  <div class="node-actions">
                    <button mat-icon-button (click)="viewNestedRecipe(node)" matTooltip="View Recipe">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button (click)="editIngredient(node)" matTooltip="Edit">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="removeIngredient(node)" matTooltip="Remove">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </mat-tree-node>
            </mat-tree>

            <!-- Recipe Summary -->
            <div class="recipe-summary" *ngIf="recipeSummary">
              <h4>Recipe Summary</h4>
              <div class="summary-grid">
                <div class="summary-item">
                  <span class="label">Total Material Cost:</span>
                  <span class="value">\${{ recipeSummary.totalMaterialCost.toFixed(2) }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">Total Recipe Cost:</span>
                  <span class="value">\${{ recipeSummary.totalRecipeCost.toFixed(2) }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">Cost per Unit:</span>
                  <span class="value">\${{ recipeSummary.costPerUnit.toFixed(2) }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">Recipe Depth:</span>
                  <span class="value">{{ recipeSummary.maxDepth }} levels</span>
                </div>
              </div>
            </div>

            <!-- Validation Messages -->
            <div class="validation-messages" *ngIf="validationMessages.length > 0">
              <h4>Validation Results</h4>
              <div class="message" 
                   *ngFor="let message of validationMessages" 
                   [class.error]="message.type === 'error'"
                   [class.warning]="message.type === 'warning'"
                   [class.success]="message.type === 'success'">
                <mat-icon>{{ getMessageIcon(message.type) }}</mat-icon>
                <span>{{ message.text }}</span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button mat-button (click)="cancel()">Cancel</button>
        <button mat-raised-button color="primary" (click)="saveRecipe()" [disabled]="!canSave()">
          <mat-icon>save</mat-icon>
          {{ isEditMode ? 'Update Recipe' : 'Create Recipe' }}
        </button>
      </div>
    </div>
  `,
  styles: [`
    .recipe-editor-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
    }

    .recipe-header-card {
      margin-bottom: 20px;
    }

    .editor-layout {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;
    }

    .recipe-form-card,
    .recipe-tree-card {
      height: fit-content;
    }

    .recipe-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .form-row mat-form-field {
      flex: 1;
    }

    .full-width {
      width: 100%;
    }

    .tree-actions {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
    }

    .recipe-tree {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      max-height: 400px;
      overflow-y: auto;
    }

    .tree-node {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      min-height: 48px;
    }

    .tree-node.nested-recipe {
      background-color: #f3e5f5;
    }

    .tree-node.raw-material {
      background-color: #e8f5e8;
    }

    .node-icon {
      margin-right: 8px;
    }

    .node-name {
      flex: 1;
      font-weight: 500;
    }

    .node-quantity {
      margin-right: 12px;
      color: #666;
      font-size: 0.9em;
    }

    .node-cost {
      margin-right: 12px;
      font-weight: 500;
      color: #2e7d32;
    }

    .nested-indicator {
      margin-right: 8px;
      color: #9c27b0;
    }

    .node-actions {
      display: flex;
      gap: 4px;
    }

    .recipe-summary {
      margin-top: 20px;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-top: 12px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
    }

    .summary-item .label {
      font-weight: 500;
    }

    .summary-item .value {
      color: #2e7d32;
      font-weight: 600;
    }

    .validation-messages {
      margin-top: 20px;
    }

    .message {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      margin: 4px 0;
      border-radius: 4px;
      gap: 8px;
    }

    .message.error {
      background-color: #ffebee;
      color: #c62828;
    }

    .message.warning {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .message.success {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 20px;
    }

    @media (max-width: 1200px) {
      .editor-layout {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class RecipeEditorComponent implements OnInit {
  private fb = inject(FormBuilder);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);
  private dialog = inject(MatDialog);
  private recipeService = inject(RecipeService);
  private productService = inject(ProductService);

  recipeForm!: FormGroup;
  recipe?: Recipe;
  isEditMode = false;
  availableProducts: Product[] = [];
  
  // Tree control for recipe structure
  treeControl = new NestedTreeControl<RecipeTreeNode>(node => node.children);
  treeDataSource = new MatTreeNestedDataSource<RecipeTreeNode>();

  recipeSummary?: {
    totalMaterialCost: number;
    totalRecipeCost: number;
    costPerUnit: number;
    maxDepth: number;
  };

  validationMessages: Array<{type: 'error' | 'warning' | 'success', text: string}> = [];

  ngOnInit() {
    this.initializeForm();
    this.loadAvailableProducts();
    
    const recipeId = this.route.snapshot.paramMap.get('id');
    if (recipeId) {
      this.isEditMode = true;
      this.loadRecipe(+recipeId);
    }
  }

  private initializeForm() {
    this.recipeForm = this.fb.group({
      productId: ['', Validators.required],
      recipeName: ['', Validators.required],
      description: [''],
      outputQuantity: [1, [Validators.required, Validators.min(0.01)]],
      outputUnit: ['', Validators.required],
      estimatedProductionTimeMinutes: [60, [Validators.required, Validators.min(1)]],
      laborCost: [0, [Validators.min(0)]],
      overheadCost: [0, [Validators.min(0)]],
      ingredients: this.fb.array([])
    });
  }

  private async loadAvailableProducts() {
    try {
      this.availableProducts = await this.productService.getProductionProducts();
    } catch (error) {
      this.snackBar.open('Failed to load products', 'Close', { duration: 3000 });
    }
  }

  private async loadRecipe(recipeId: number) {
    try {
      this.recipe = await this.recipeService.getRecipeWithIngredients(recipeId);
      if (this.recipe) {
        this.populateForm();
        this.buildRecipeTree();
        this.calculateSummary();
      }
    } catch (error) {
      this.snackBar.open('Failed to load recipe', 'Close', { duration: 3000 });
    }
  }

  private populateForm() {
    if (!this.recipe) return;

    this.recipeForm.patchValue({
      productId: this.recipe.productId,
      recipeName: this.recipe.recipeName,
      description: this.recipe.description,
      outputQuantity: this.recipe.outputQuantity,
      outputUnit: this.recipe.outputUnit,
      estimatedProductionTimeMinutes: this.recipe.estimatedProductionTimeMinutes,
      laborCost: this.recipe.laborCost,
      overheadCost: this.recipe.overheadCost
    });
  }

  private buildRecipeTree() {
    if (!this.recipe?.ingredients) return;

    const treeData: RecipeTreeNode[] = this.recipe.ingredients.map(ingredient => ({
      id: ingredient.recipeIngredientId,
      name: `${ingredient.ingredientProduct.productCode} - ${ingredient.ingredientProduct.productName}`,
      quantity: ingredient.requiredQuantity,
      unit: ingredient.requiredUnit,
      cost: ingredient.totalCost,
      isNestedRecipe: ingredient.isNestedRecipe,
      productId: ingredient.ingredientProductId,
      children: ingredient.isNestedRecipe ? this.buildNestedRecipeChildren(ingredient) : []
    }));

    this.treeDataSource.data = treeData;
    this.treeControl.expandAll();
  }

  private buildNestedRecipeChildren(ingredient: RecipeIngredient): RecipeTreeNode[] {
    // This would recursively build the tree for nested recipes
    // For now, returning empty array - would need to fetch nested recipe data
    return [];
  }

  hasChild = (_: number, node: RecipeTreeNode) => !!node.children && node.children.length > 0;

  addIngredient() {
    // Open dialog to add new ingredient
    // Implementation would show a dialog with product selection
    console.log('Add ingredient dialog');
  }

  editIngredient(node: RecipeTreeNode) {
    // Open dialog to edit ingredient
    console.log('Edit ingredient:', node);
  }

  removeIngredient(node: RecipeTreeNode) {
    // Remove ingredient from recipe
    console.log('Remove ingredient:', node);
  }

  viewNestedRecipe(node: RecipeTreeNode) {
    // Navigate to view the nested recipe
    this.router.navigate(['/recipes', node.productId, 'view']);
  }

  async validateRecipe() {
    this.validationMessages = [];
    
    try {
      const validation = await this.recipeService.validateRecipe(this.recipeForm.value);
      
      if (validation.isValid) {
        this.validationMessages.push({
          type: 'success',
          text: 'Recipe validation passed successfully!'
        });
      } else {
        validation.errors.forEach(error => {
          this.validationMessages.push({
            type: 'error',
            text: error
          });
        });
        
        validation.warnings.forEach(warning => {
          this.validationMessages.push({
            type: 'warning',
            text: warning
          });
        });
      }
    } catch (error) {
      this.validationMessages.push({
        type: 'error',
        text: 'Failed to validate recipe'
      });
    }
  }

  private calculateSummary() {
    if (!this.recipe) return;

    this.recipeSummary = {
      totalMaterialCost: this.recipe.totalMaterialCost,
      totalRecipeCost: this.recipe.totalRecipeCost,
      costPerUnit: this.recipe.costPerUnit,
      maxDepth: this.calculateRecipeDepth()
    };
  }

  private calculateRecipeDepth(): number {
    // Calculate the maximum depth of nested recipes
    // This would traverse the recipe tree to find the deepest level
    return 1; // Placeholder
  }

  canSave(): boolean {
    return this.recipeForm.valid && this.validationMessages.every(m => m.type !== 'error');
  }

  async saveRecipe() {
    if (!this.canSave()) return;

    try {
      const recipeData = this.recipeForm.value;
      
      if (this.isEditMode && this.recipe) {
        await this.recipeService.updateRecipe(this.recipe.recipeId, recipeData);
        this.snackBar.open('Recipe updated successfully', 'Close', { duration: 3000 });
      } else {
        await this.recipeService.createRecipe(recipeData);
        this.snackBar.open('Recipe created successfully', 'Close', { duration: 3000 });
      }
      
      this.router.navigate(['/recipes']);
    } catch (error) {
      this.snackBar.open('Failed to save recipe', 'Close', { duration: 3000 });
    }
  }

  cancel() {
    this.router.navigate(['/recipes']);
  }

  getMessageIcon(type: string): string {
    switch (type) {
      case 'error': return 'error';
      case 'warning': return 'warning';
      case 'success': return 'check_circle';
      default: return 'info';
    }
  }
}
